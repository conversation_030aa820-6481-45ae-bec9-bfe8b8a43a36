
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface ContactAndPaymentSectionProps {
  customerInfo: {
    contactMethod: string;
    contactName: string;
    paymentMethod: string;
    notes: string;
  };
  errors: Record<string, string>;
  handleCustomerInfoChange: (name: string, value: string) => void;
}

const ContactAndPaymentSection: React.FC<ContactAndPaymentSectionProps> = ({
  customerInfo,
  errors,
  handleCustomerInfoChange,
}) => {
  return (
    <>
      <div className="space-y-1">
        <Label htmlFor="contactMethod">聯繫方式 *</Label>
        <Select 
          value={customerInfo.contactMethod}
          onValueChange={(value) => handleCustomerInfoChange("contactMethod", value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="請選擇聯繫方式" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="臉書粉絲團">臉書粉絲團</SelectItem>
            <SelectItem value="LINE官方">LINE官方</SelectItem>
          </SelectContent>
        </Select>
        {errors.contactMethod && (
          <p className="text-red-500 text-xs">{errors.contactMethod}</p>
        )}
      </div>

      <div className="space-y-1">
        <Label htmlFor="contactName">聯繫帳號名稱 *</Label>
        <Input
          id="contactName"
          name="contactName"
          value={customerInfo.contactName}
          onChange={(e) => handleCustomerInfoChange("contactName", e.target.value)}
          className={errors.contactName ? "border-red-500" : ""}
          placeholder="請輸入您的臉書/LINE名稱"
        />
        {errors.contactName && (
          <p className="text-red-500 text-xs">{errors.contactName}</p>
        )}
      </div>

      <div className="space-y-1">
        <Label htmlFor="paymentMethod">付款方式 *</Label>
        <Select 
          value={customerInfo.paymentMethod}
          onValueChange={(value) => handleCustomerInfoChange("paymentMethod", value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="請選擇付款方式" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="貨到付款">貨到付款</SelectItem>
            <SelectItem value="銀行轉帳">銀行轉帳</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-1 sm:col-span-2">
        <Label htmlFor="notes">備註</Label>
        <Textarea
          id="notes"
          name="notes"
          value={customerInfo.notes}
          onChange={(e) => handleCustomerInfoChange("notes", e.target.value)}
          placeholder="如有特殊需求請在此說明"
          className="min-h-[80px] text-base"
        />
      </div>
    </>
  );
};

export default ContactAndPaymentSection;
