/**
 * 訂購說明組件
 * 展示訂購須知、配送資訊和保存方式
 */
function OrderInstructions() {
    try {
        // 訂購須知資料
        const orderRules = [
            {
                icon: "fas fa-clock",
                text: "請提前1-2天預訂，以確保新鮮製作"
            },
            {
                icon: "fas fa-shipping-fast",
                text: "若您有指定日期到貨，有少許可能會早或晚1-2天到貨(以商家出貨日為主)"
            },
            {
                icon: "fas fa-gift",
                text: "訂購滿$350免運費"
            },
            {
                icon: "fas fa-star",
                text: [
                    "如果您的訂單未能在預計到貨日送達，您可以直接拒收商品。",
                    "很抱歉！這是我們的疏失，絕不會讓您承擔任何損失。",
                    "預先轉帳付款的顧客，我們將會全額退款給您。"
                ]
            }
        ];

        // 付款方式資料
        const paymentInfo = [
            {
                title: "貨到付款",
                items: [
                    "宅配到府",
                    "7-11門市",
                    "來店自取"
                ]
            },
            {
                title: "銀行轉帳匯款",
                items: [
                    "中國信託銀行 (代碼：822)",
                    "帳號：222540600078",
                    "轉帳後請提供後五碼確認",
                    "確認後即安排出貨"
                ]
            }
        ];

        return (
            <section className="order-instructions-section py-16 bg-gray-50" data-name="order-instructions" id="order-info">
                <div className="container mx-auto px-4" data-name="instructions-container">
                    {/* 標題區塊 */}
                    <div className="text-center mb-12" data-name="instructions-header">
                        <h2 className="section-title text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                            訂購與配送
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            詳細的訂購說明與保存方式，讓您輕鬆享受美味
                        </p>
                    </div>

                    {/* 主要內容區塊 */}
                    <div className="bg-white rounded-lg shadow-lg p-8 max-w-6xl mx-auto" data-name="instructions-content">
                        <div className="grid md:grid-cols-2 gap-8">
                            {/* 訂購須知 */}
                            <div className="order-rules" data-name="order-rules">
                                <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                                    <i className="fas fa-info-circle text-red-600 mr-3"></i>
                                    訂購須知
                                </h3>
                                <div className="space-y-2">
                                    {orderRules.map((rule, index) => (
                                        <div
                                            key={index}
                                            className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                                            data-name={`order-rule-${index}`}
                                        >
                                            <i className={`${rule.icon} text-red-600 mt-1 flex-shrink-0`}></i>
                                            <span className="text-gray-700 leading-snug">{rule.text}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* 付款方式 */}
                            <div className="payment-info" data-name="payment-info">
                                <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                                    <i className="fas fa-credit-card text-blue-600 mr-3"></i>
                                    付款方式
                                </h3>
                                <div className="space-y-6">
                                    {paymentInfo.map((info, index) => (
                                        <div
                                            key={index}
                                            className="payment-item"
                                            data-name={`payment-item-${index}`}
                                        >
                                            <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                                                <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                                                {info.title}
                                            </h4>
                                            <ul className="space-y-2 ml-4">
                                                {info.items.map((item, itemIndex) => (
                                                    <li
                                                        key={itemIndex}
                                                        className="text-gray-600 flex items-center"
                                                    >
                                                        <i className="fas fa-check text-green-600 mr-2 text-sm"></i>
                                                        {item}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* 配送資訊 */}
                        <div className="delivery-info mt-8 pt-8 border-t border-gray-200" data-name="delivery-info">
                            <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center">
                                <i className="fas fa-truck text-green-600 mr-3"></i>
                                配送資訊
                            </h3>
                            <div className="grid md:grid-cols-3 gap-6">
                                <div className="text-center p-4 rounded-lg bg-green-50">
                                    <i className="fas fa-home text-green-600 text-2xl mb-3"></i>
                                    <h4 className="font-semibold text-gray-800 mb-2">宅配到府</h4>
                                    <p className="text-sm text-gray-600">專業配送，直達您家</p>
                                </div>
                                <div className="text-center p-4 rounded-lg bg-blue-50">
                                    <i className="fas fa-store text-blue-600 text-2xl mb-3"></i>
                                    <h4 className="font-semibold text-gray-800 mb-2">超商取貨</h4>
                                    <p className="text-sm text-gray-600">7-11便利取貨</p>
                                </div>
                                <div className="text-center p-4 rounded-lg bg-purple-50">
                                    <i className="fas fa-clock text-purple-600 text-2xl mb-3"></i>
                                    <h4 className="font-semibold text-gray-800 mb-2">來店自取</h4>
                                    <p className="text-sm text-gray-600">西螺鎮有2個自取點</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('OrderInstructions component error:', error);
        reportError(error);
        return (
            <section className="order-instructions-section py-16 bg-gray-50">
                <div className="container mx-auto px-4 text-center">
                    <p className="text-gray-500">訂購說明載入中...</p>
                </div>
            </section>
        );
    }
}
