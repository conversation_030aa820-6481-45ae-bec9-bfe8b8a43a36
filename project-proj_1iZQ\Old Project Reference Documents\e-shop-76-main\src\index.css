
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&family=Noto+Serif+TC:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 32 54% 90%;
    --foreground: 35 40% 15%;

    --card: 32 54% 95%;
    --card-foreground: 35 40% 15%;

    --popover: 32 54% 95%;
    --popover-foreground: 35 40% 15%;

    --primary: 28 73% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 35 88% 68%;
    --secondary-foreground: 35 40% 15%;

    --muted: 32 30% 90%;
    --muted-foreground: 35 30% 30%;

    --accent: 28 60% 75%;
    --accent-foreground: 35 40% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 35 50% 80%;
    --input: 35 50% 80%;
    --ring: 28 73% 60%;

    --radius: 0.5rem;

    --sidebar-background: 32 54% 90%;
    --sidebar-foreground: 35 40% 15%;
    --sidebar-primary: 28 73% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 35 88% 68%;
    --sidebar-accent-foreground: 35 40% 15%;
    --sidebar-border: 35 50% 80%;
    --sidebar-ring: 28 73% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-radish-light text-foreground font-serif text-base leading-relaxed;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-bold text-radish-darkest leading-tight;
  }
}

/* 新增：針對 section 縮小 mobile padding，更精簡間距，標題居中 */
@layer components {
  .btn-primary {
    @apply bg-radish-darker text-white px-6 py-3 rounded-md text-base hover:bg-radish-darkest transition-colors;
  }

  .btn-secondary {
    @apply bg-radish text-radish-darkest px-6 py-3 rounded-md text-base hover:bg-radish-dark transition-colors;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8;
  }

  .section-title {
    @apply text-xl sm:text-2xl md:text-3xl font-bold text-radish-darkest mb-4 text-center tracking-tight;
  }
  .section-subtitle {
    @apply text-lg sm:text-xl md:text-2xl font-semibold text-radish-darker mb-3;
  }
}

/* 行動裝置強化：section 通用 padding、縮排，表單欄位間距優化 */
@media (max-width: 640px) {
  main, section {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .container-custom {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  form {
    gap: 0.5rem !important;
  }
  input, select, textarea {
    font-size: 1rem !important;
    min-height: 2.75rem !important;
  }
}
