/**
 * 製作過程區塊組件
 * 展示傳統工藝製作流程
 */
function ProcessSection() {
    try {
        // 製作過程步驟資料
        const processSteps = [
            {
                id: 1,
                title: "優質食材",
                description: "精選新鮮白蘿蔔與在地優質食材",
                image: "https://767780.xyz/images/IMG_3828.jpg.webp",
                icon: "fas fa-carrot"
            },
            {
                id: 2,
                title: "傳統工藝",
                description: "承襲古法，堅持手工製作工序",
                image: "https://767780.xyz/images/IMG_3827.jpg.webp",
                icon: "fas fa-hands"
            },
            {
                id: 3,
                title: "精心蒸製",
                description: "控制火候，蒸製出完美口感",
                image: "https://767780.xyz/images/IMG_3832.jpg.webp",
                icon: "fas fa-fire"
            },
            {
                id: 4,
                title: "現做現送",
                description: "新鮮製作，確保最佳品質",
                image: "https://767780.xyz/images/IMG_3825.jpg.webp",
                icon: "fas fa-truck"
            }
        ];

        return (
            <section className="process-section py-16 bg-white" data-name="process-section" id="process">
                <div className="container mx-auto px-4" data-name="process-container">
                    {/* 標題區塊 */}
                    <div className="text-center mb-12" data-name="process-header">
                        <h2 className="section-title text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                            傳統工藝，匠心製作
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            堅持傳統手工製作工藝，每一個步驟都用心把關，為您呈現最道地的古早味
                        </p>
                    </div>

                    {/* 製作步驟網格 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" data-name="process-grid">
                        {processSteps.map((step, index) => (
                            <div
                                key={step.id}
                                className="process-step text-center group"
                                data-name={`process-step-${step.id}`}
                            >
                                {/* 圖片容器 */}
                                <div className="process-image-container relative mb-6 overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                                    <img
                                        src={step.image}
                                        alt={step.title}
                                        className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                                        data-name={`process-image-${step.id}`}
                                    />
                                    {/* 圖標覆蓋層 */}
                                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        <i className={`${step.icon} text-4xl text-white`}></i>
                                    </div>
                                </div>

                                {/* 文字內容 */}
                                <div className="process-content" data-name={`process-content-${step.id}`}>
                                    <h3 className="text-xl font-bold text-gray-800 mb-2">
                                        {step.title}
                                    </h3>
                                    <p className="text-gray-600 text-sm leading-relaxed">
                                        {step.description}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* 底部說明 */}
                    <div className="text-center mt-12" data-name="process-footer">
                        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl shadow-xl p-6 max-w-4xl mx-auto">
                            <h3 className="text-xl font-bold text-gray-800 mb-3">
                                堅持品質，傳承美味
                            </h3>
                            <p className="text-gray-600 leading-relaxed">
                                從食材挑選到成品完成，每一個環節都經過嚴格把關。我們相信，只有用心製作的食物，
                                才能帶給您最真實的古早味體驗。這不只是一塊蘿蔔糕，更是我們對傳統工藝的堅持與傳承。
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('ProcessSection component error:', error);
        reportError(error);
        return (
            <section className="process-section py-16 bg-white">
                <div className="container mx-auto px-4 text-center">
                    <p className="text-gray-500">製作過程載入中...</p>
                </div>
            </section>
        );
    }
}
