
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import FloatingActions from '../components/FloatingActions';
import { products } from '../data/products';

const Index = () => {
  // 模擬圖片
  const bannerImages = [
    "/placeholder.svg",
    "/placeholder.svg",
    "/placeholder.svg"
  ];

  const processImages = [
    { src: "/placeholder.svg", title: "優質食材" },
    { src: "/placeholder.svg", title: "傳統工藝" },
    { src: "/placeholder.svg", title: "精心蒸製" },
    { src: "/placeholder.svg", title: "現做現送" }
  ];

  return (
    <>
      <Header />
      <main>
        {/* Banner Section */}
        <section className="relative bg-radish-light py-12 md:py-20">
          <div className="container-custom">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-radish-darkest">
                  融氏手工蘿蔔糕<br />
                  <span className="text-radish-darker">傳統美味，用心製作</span>
                </h1>
                <p className="text-lg mb-8">
                  精選新鮮食材，堅持手工製作，每一口都能品嚐到傳統的滋味。我們用心維持傳統工藝，為您帶來最道地的美食體驗。
                </p>
                <div className="flex space-x-4">
                  <Link to="/products" className="btn-secondary">
                    了解更多
                  </Link>
                  <Link to="/order" className="btn-primary">
                    立即訂購
                  </Link>
                </div>
              </div>
              <div className="relative h-64 md:h-96">
                <div className="absolute inset-0 flex">
                  {bannerImages.map((img, index) => (
                    <div 
                      key={index} 
                      className="w-1/3 p-2"
                      style={{ transform: `translateY(${index % 2 === 0 ? '10%' : '0'})` }}
                    >
                      <img 
                        src={img} 
                        alt="蘿蔔糕展示" 
                        className="w-full h-full object-cover rounded-lg shadow-lg"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 製作過程 */}
        <section className="py-16 bg-white">
          <div className="container-custom">
            <h2 className="section-title">傳統工藝，匠心製作</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
              {processImages.map((item, index) => (
                <div key={index} className="text-center">
                  <div className="h-48 mb-4 overflow-hidden rounded-lg">
                    <img 
                      src={item.src} 
                      alt={item.title}
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                    />
                  </div>
                  <h3 className="text-xl font-semibold text-radish-darker">{item.title}</h3>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 產品展示 */}
        <section className="py-16 bg-radish-light">
          <div className="container-custom">
            <h2 className="section-title">我們的產品</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mt-8">
              {products.map((product) => (
                <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-48 overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold mb-2 text-radish-darkest">{product.name}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{product.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-radish-darker">${product.price}</span>
                      <Link to={`/products/${product.id}`} className="text-radish-darkest hover:text-radish-darker flex items-center">
                        詳細資訊 <ArrowRight className="w-4 h-4 ml-1" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="text-center mt-10">
              <Link to="/products" className="btn-primary inline-block">
                查看所有產品
              </Link>
            </div>
          </div>
        </section>

        {/* 訂購說明 */}
        <section className="py-16 bg-white">
          <div className="container-custom">
            <h2 className="section-title">訂購與配送</h2>
            <div className="bg-radish p-8 rounded-lg shadow-md">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="section-subtitle">訂購須知</h3>
                  <ul className="list-disc list-inside space-y-2 text-radish-darkest">
                    <li>請提前1-2天預訂，以確保新鮮製作</li>
                    <li>訂購滿$350免運費</li>
                    <li>原味蘿蔔糕購買2樣以上免運費</li>
                    <li>港式蘿蔔糕與芋頭糕1樣即可免運費</li>
                    <li>鳳梨豆腐乳購買2瓶以上免運費</li>
                  </ul>
                </div>
                <div>
                  <h3 className="section-subtitle">保存方式</h3>
                  <p className="mb-4 text-radish-darkest">
                    蘿蔔糕和芋頭糕冷藏可保存5天，冷凍可保存1個月。食用前蒸熱或煎熱即可。
                  </p>
                  <p className="text-radish-darkest">
                    鳳梨豆腐乳開封後請冷藏保存，可保存3個月。
                  </p>
                </div>
              </div>
              <div className="mt-8 text-center">
                <Link to="/order" className="btn-primary">
                  立即訂購
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* 客戶評價 */}
        <section className="py-16 bg-radish-light">
          <div className="container-custom">
            <h2 className="section-title">顧客評價</h2>
            <div className="grid md:grid-cols-3 gap-6 mt-8">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-white p-6 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-radish rounded-full flex items-center justify-center text-radish-darkest font-bold text-lg mr-4">
                      {["王", "林", "陳"][item - 1]}
                    </div>
                    <div>
                      <h4 className="font-semibold text-radish-darker">
                        {["王小姐", "林先生", "陳太太"][item - 1]}
                      </h4>
                      <div className="flex text-yellow-500">
                        {[...Array(5)].map((_, i) => (
                          <span key={i}>★</span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600">
                    {[
                      "真的很好吃！蘿蔔的香氣很濃郁，口感Q彈，煎過之後外酥內軟，全家都很喜歡。",
                      "從小吃到大的古早味，每次回雲林都會買，現在可以直接宅配真的太方便了！",
                      "芋頭糕真的超讚，芋頭的香氣十足，吃了會讓人一直想再吃，已經回購好幾次了。"
                    ][item - 1]}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 聯絡我們 */}
        <section className="py-16 bg-white">
          <div className="container-custom">
            <h2 className="section-title">聯絡我們</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-radish-light p-6 rounded-lg">
                <h3 className="section-subtitle mb-6">營業資訊</h3>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <span className="font-semibold w-24">營業時間：</span>
                    <span>週一至週日 9:00-22:00</span>
                  </li>
                  <li className="flex items-start">
                    <span className="font-semibold w-24">聯絡電話：</span>
                    <span>0933-477226<br />0968-789607</span>
                  </li>
                  <li className="flex items-start">
                    <span className="font-semibold w-24">地址：</span>
                    <span>雲林縣西螺鎮中山路302-3號</span>
                  </li>
                </ul>
              </div>
              <div>
                <iframe 
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3646.5728465034727!2d120.4596317!3d23.7978533!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x346e9ce66944a595%3A0xb32da6fca7cd0220!2zNjQ45a6c5p6X57ij6KW_5aKv6Y6u5Lit5bGx6LevMzAy!5e0!3m2!1szh-TW!2stw!4v1663925526025!5m2!1szh-TW!2stw" 
                  className="w-full h-80 rounded-lg"
                  style={{ border: 0 }}
                  allowFullScreen 
                  loading="lazy"
                  title="店鋪位置"
                ></iframe>
              </div>
            </div>
          </div>
        </section>
      </main>
      <FloatingActions />
      <Footer />
    </>
  );
};

export default Index;
