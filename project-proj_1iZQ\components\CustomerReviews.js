/**
 * 客戶評價組件
 * 展示顧客真實評價與推薦
 */
function CustomerReviews() {
    try {
        // 客戶評價資料
        const reviews = [
            {
                id: 1,
                name: "王小姐",
                avatar: "王",
                rating: 5,
                comment: "真的很好吃！蘿蔔的香氣很濃郁，口感Q彈，煎過之後外酥內軟，全家都很喜歡。每次訂購都很新鮮，品質很穩定！",
                product: "古早味蘿蔔糕",
                date: "2024年1月"
            },
            {
                id: 2,
                name: "林先生",
                avatar: "林",
                rating: 5,
                comment: "從小吃到大的古早味，每次回雲林都會買，現在可以直接宅配真的太方便了！味道跟記憶中的一模一樣，很感動。",
                product: "傳統手工蘿蔔糕",
                date: "2024年1月"
            },
            {
                id: 3,
                name: "陳太太",
                avatar: "陳",
                rating: 5,
                comment: "芋頭糕真的超讚，芋頭的香氣十足，吃了會讓人一直想再吃，已經回購好幾次了。包裝也很用心，收到時還是很新鮮。",
                product: "芋頭糕",
                date: "2024年1月"
            },
            {
                id: 4,
                name: "張先生",
                avatar: "張",
                rating: 5,
                comment: "港式蘿蔔糕的口感很特別，比一般的更有層次感。配送速度很快，客服態度也很好，會繼續支持！",
                product: "港式蘿蔔糕",
                date: "2024年1月"
            },
            {
                id: 5,
                name: "李小姐",
                avatar: "李",
                rating: 5,
                comment: "鳳梨豆腐乳搭配蘿蔔糕真的是絕配！酸甜的口感很開胃，而且保存期限也很長，很實用。",
                product: "鳳梨豆腐乳",
                date: "2024年1月"
            },
            {
                id: 6,
                name: "黃太太",
                avatar: "黃",
                rating: 5,
                comment: "手工製作的品質真的看得出來，每一塊都很扎實，蒸熱後香氣四溢。價格也很合理，CP值很高！",
                product: "手工蘿蔔糕",
                date: "2024年1月"
            }
        ];

        // 渲染星級評分
        const renderStars = (rating) => {
            return Array.from({ length: 5 }, (_, index) => (
                <i 
                    key={index} 
                    className={`fas fa-star ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
                ></i>
            ));
        };

        return (
            <section className="customer-reviews-section py-16 bg-gradient-to-br from-red-50 to-orange-50" data-name="customer-reviews" id="reviews">
                <div className="container mx-auto px-4" data-name="reviews-container">
                    {/* 標題區塊 */}
                    <div className="text-center mb-12" data-name="reviews-header">
                        <h2 className="section-title text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                            顧客評價
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            聽聽我們的顧客怎麼說，真實的評價是我們持續進步的動力
                        </p>
                        {/* 統計資訊 */}
                        <div className="flex justify-center items-center mt-6 space-x-8">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">5.0</div>
                                <div className="flex justify-center mb-1">
                                    {renderStars(5)}
                                </div>
                                <div className="text-sm text-gray-500">平均評分</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">500+</div>
                                <div className="text-sm text-gray-500">滿意顧客</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-red-600">98%</div>
                                <div className="text-sm text-gray-500">回購率</div>
                            </div>
                        </div>
                    </div>

                    {/* 評價網格 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-name="reviews-grid">
                        {reviews.map((review) => (
                            <div 
                                key={review.id} 
                                className="review-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                                data-name={`review-${review.id}`}
                            >
                                {/* 顧客資訊 */}
                                <div className="flex items-center mb-4" data-name="customer-info">
                                    <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4 shadow-md">
                                        {review.avatar}
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-800 text-lg">
                                            {review.name}
                                        </h4>
                                        <div className="flex items-center space-x-2">
                                            <div className="flex">
                                                {renderStars(review.rating)}
                                            </div>
                                            <span className="text-sm text-gray-500">{review.date}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* 產品標籤 */}
                                <div className="mb-3">
                                    <span className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                                        {review.product}
                                    </span>
                                </div>

                                {/* 評價內容 */}
                                <p className="text-gray-600 leading-relaxed text-sm">
                                    "{review.comment}"
                                </p>

                                {/* 有用按鈕 */}
                                <div className="mt-4 pt-4 border-t border-gray-100">
                                    <button className="text-sm text-gray-500 hover:text-red-600 transition-colors flex items-center">
                                        <i className="fas fa-thumbs-up mr-1"></i>
                                        有用
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* 底部行動呼籲 */}
                    <div className="text-center mt-12" data-name="reviews-cta">
                        <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl mx-auto">
                            <h3 className="text-2xl font-bold text-gray-800 mb-4">
                                加入滿意顧客的行列
                            </h3>
                            <p className="text-gray-600 mb-6">
                                體驗傳統手工製作的美味，讓我們為您帶來最道地的古早味
                            </p>
                            <a 
                                href="#order" 
                                className="inline-flex items-center bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors shadow-lg"
                            >
                                <i className="fas fa-heart mr-2"></i>
                                立即體驗
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('CustomerReviews component error:', error);
        reportError(error);
        return (
            <section className="customer-reviews-section py-16 bg-gradient-to-br from-red-50 to-orange-50">
                <div className="container mx-auto px-4 text-center">
                    <p className="text-gray-500">客戶評價載入中...</p>
                </div>
            </section>
        );
    }
}
