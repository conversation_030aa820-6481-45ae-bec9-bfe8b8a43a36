body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    color: #2d3748;
    line-height: 1.6;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
}

.section {
    padding: 3rem 0;
}

@media (min-width: 768px) {
    .section {
        padding: 5rem 0;
    }
}

.section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #2d3748;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (min-width: 768px) {
    .section-title {
        font-size: 2.5rem;
        margin-bottom: 3rem;
    }
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 1.125rem;
    box-shadow: 0 4px 14px 0 rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(45deg, #e53e3e, #c53030);
    color: white;
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #c53030, #9c2626);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px 0 rgba(229,62,62,0.3);
}

.shadow-custom {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

input, select, textarea, button {
    margin: 0.25rem 0;
}

button, .btn, a {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
}

input[type="text"], input[type="tel"], input[type="email"], input[type="number"], select, textarea {
    font-size: 16px;
    padding: 0.75rem;
    width: 100%;
    border-radius: 0.5rem;
    border: 2px solid #e2e8f0;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229,62,62,0.1);
}

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

/* 新增區塊的通用樣式 */
.section-enhanced {
    position: relative;
    overflow: hidden;
}

.section-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.8) 100%);
    z-index: -1;
}

/* 卡片樣式增強 */
.card-enhanced {
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

/* 按鈕樣式增強 */
.btn-enhanced {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
    color: white;
    text-decoration: none;
}

/* 標題樣式增強 */
.title-enhanced {
    position: relative;
    display: inline-block;
    color: #1f2937;
    font-weight: 700;
}

.title-enhanced::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #dc2626 0%, #f59e0b 100%);
    border-radius: 2px;
}

/* 響應式字體大小 */
@media (max-width: 640px) {
    .section-title {
        font-size: 1.75rem;
    }

    .btn-enhanced {
        padding: 10px 20px;
        font-size: 14px;
    }
}
