/* 製作過程區塊樣式 */
.process-section {
    position: relative;
    overflow: hidden;
}

.process-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    z-index: -1;
}

/* 製作步驟卡片 */
.process-step {
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
}

/* 圖片容器 */
.process-image-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.process-step:hover .process-image-container {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.process-image-container img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.process-step:hover .process-image-container img {
    transform: scale(1.05);
}

/* 步驟編號 */
.process-image-container .absolute.top-4.left-4 {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    font-weight: 700;
    transition: transform 0.3s ease;
}

.process-step:hover .process-image-container .absolute.top-4.left-4 {
    transform: scale(1.1);
}

/* 圖標覆蓋層 */
.process-image-container .absolute.inset-0 {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 100%);
    backdrop-filter: blur(2px);
}

/* 內容區域 */
.process-content h3 {
    color: #1f2937;
    font-weight: 700;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.process-step:hover .process-content h3 {
    color: #dc2626;
}

.process-content p {
    color: #6b7280;
    line-height: 1.6;
    font-size: 14px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .process-section {
        padding: 3rem 0;
    }
    
    .process-image-container {
        margin-bottom: 1rem;
    }
    
    .process-image-container img {
        height: 160px;
    }
    
    .process-content h3 {
        font-size: 1.1rem;
    }
    
    .process-content p {
        font-size: 13px;
    }
}

@media (max-width: 640px) {
    .process-section .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .process-image-container img {
        height: 140px;
    }
}

/* 底部說明區塊 */
.process-section .bg-gray-50 {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.process-section .bg-gray-50:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.process-section .bg-gray-50 h3 {
    color: #1f2937;
    font-weight: 700;
}

.process-section .bg-gray-50 p {
    color: #4b5563;
    line-height: 1.7;
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.process-step {
    animation: fadeInUp 0.6s ease forwards;
}

.process-step:nth-child(1) { animation-delay: 0.1s; }
.process-step:nth-child(2) { animation-delay: 0.2s; }
.process-step:nth-child(3) { animation-delay: 0.3s; }
.process-step:nth-child(4) { animation-delay: 0.4s; }

/* 標題樣式增強 */
.process-section .section-title {
    position: relative;
    display: inline-block;
}

.process-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #dc2626 0%, #f59e0b 100%);
    border-radius: 2px;
}

/* 圖標樣式 */
.process-image-container i {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.process-step:hover .process-image-container i {
    transform: scale(1.2) rotate(5deg);
}
