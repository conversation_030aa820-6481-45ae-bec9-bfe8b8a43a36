
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ShoppingCart, MessageSquare, Facebook } from 'lucide-react';

const FloatingActions: React.FC = () => {
  return (
    <div className="fixed right-3 bottom-3 flex flex-col space-y-2 sm:space-y-3 z-50">
      <a
        href="#"
        className="w-11 h-11 sm:w-12 sm:h-12 bg-green-600 text-white rounded-full flex items-center justify-center shadow-md hover:bg-green-700 transition-colors"
        aria-label="Line"
      >
        <MessageSquare className="w-5 h-5 sm:w-6 sm:h-6" />
      </a>
      <a
        href="#"
        className="w-11 h-11 sm:w-12 sm:h-12 bg-blue-600 text-white rounded-full flex items-center justify-center shadow-md hover:bg-blue-700 transition-colors"
        aria-label="Facebook"
      >
        <Facebook className="w-5 h-5 sm:w-6 sm:h-6" />
      </a>
      <Link
        to="/order"
        className="w-11 h-11 sm:w-12 sm:h-12 bg-radish-darker text-white rounded-full flex items-center justify-center shadow-md hover:bg-radish-darkest transition-colors"
        aria-label="立即訂購"
      >
        <ShoppingCart className="w-5 h-5 sm:w-6 sm:h-6" />
      </Link>
    </div>
  );
};

export default FloatingActions;
