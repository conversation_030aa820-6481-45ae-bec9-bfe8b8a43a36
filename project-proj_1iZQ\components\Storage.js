function Storage() {
    try {
        return (
            <section id="storage" className="section bg-white" data-name="storage-section">
                <div className="container mx-auto px-4">
                    <h2 className="section-title" data-name="section-title">保存方式說明</h2>
                    <div className="max-w-3xl mx-auto" data-name="storage-content">
                        <div className="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-md mb-3 sm:mb-6" data-name="storage-card">
                            <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-4 text-gray-800" data-name="storage-title">
                                冷藏保存方式
                            </h3>
                            <ul className="list-disc pl-6 space-y-1 sm:space-y-2" data-name="storage-list">
                                <li className="leading-relaxed sm:leading-normal">未拆封可保存7-8天</li>
                                <li className="leading-relaxed sm:leading-normal">最佳保存溫度：2℃~5℃</li>
                                <li className="leading-relaxed sm:leading-normal">注意：純米漿製作不可冷凍</li>
                            </ul>
                        </div>

                        <div className="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-md mb-3 sm:mb-6" data-name="opened-storage-card">
                            <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-4 text-gray-800" data-name="opened-storage-title">
                                拆封後保存
                            </h3>
                            <ul className="list-disc pl-6 space-y-1 sm:space-y-2" data-name="opened-storage-list">
                                <li className="leading-relaxed sm:leading-normal">建議3天內食用完畢</li>
                                <li className="leading-relaxed sm:leading-normal">可用乾淨紙巾或布包裹，若有濕氣須及時更換</li>
                            </ul>
                        </div>

                        <div className="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-md" data-name="quality-check-card">
                            <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-4 text-gray-800" data-name="quality-check-title">
                                品質檢查
                            </h3>
                            <ul className="list-disc pl-6 space-y-1 sm:space-y-2" data-name="quality-check-list">
                                <li className="leading-relaxed sm:leading-normal">無黏液且無酸味即為正常</li>
                                <li className="leading-relaxed sm:leading-normal">避免悶熱及過多濕氣</li>
                                <li className="leading-relaxed sm:leading-normal">退冰後再冷藏會縮短保存期限</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('Storage component error:', error);
        reportError(error);
        return null;
    }
}
