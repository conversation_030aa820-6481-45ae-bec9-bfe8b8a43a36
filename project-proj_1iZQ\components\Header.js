function Header() {
    try {
        return (
            <header className="bg-white shadow-md fixed w-full top-0 z-50" data-name="header">
                <div className="container mx-auto px-4 py-4">
                    <nav className="flex items-center justify-between" data-name="nav">
                        <div className="flex items-center" data-name="logo-container">
                            <img
                                src="https://767780.xyz/images/LOGO-1.webp"
                                alt="融氏古早味手工蘿蔔糕"
                                className="h-12"
                                data-name="logo"
                            />
                            <h1 className="ml-3 text-xl font-bold text-gray-800" data-name="brand-name">
                                古早味手工蘿蔔糕
                            </h1>
                        </div>
                        <div className="hidden md:flex space-x-6" data-name="nav-links">
                        <a href="#process" className="text-gray-600 hover:text-red-600 transition-colors" data-name="nav-link">製作過程</a>
                            <a href="#products" className="text-gray-600 hover:text-red-600 transition-colors" data-name="nav-link">產品介紹</a>
                            <a href="#reviews" className="text-gray-600 hover:text-red-600 transition-colors" data-name="nav-link">客戶評價</a>
                            <a href="#storage" className="text-gray-600 hover:text-red-600 transition-colors" data-name="nav-link">保存方式</a>
                            <a href="#order-info" className="text-gray-600 hover:text-red-600 transition-colors" data-name="nav-link">訂購說明</a>
                            <a href="#order" className="text-gray-600 hover:text-red-600 transition-colors" data-name="nav-link">立即訂購</a>
                        </div>
                    </nav>
                </div>
            </header>
        );
    } catch (error) {
        console.error('Header component error:', error);
        reportError(error);
        return null;
    }
}
