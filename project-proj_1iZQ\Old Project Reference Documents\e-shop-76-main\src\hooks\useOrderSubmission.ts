import { useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import { products } from '../data/products';
import { type OrderItem, type CustomerInfo } from './useOrderForm';

export const useOrderSubmission = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const calculateTotal = (orderItems: OrderItem[]) => {
    let total = 0;
    orderItems.forEach(item => {
      const product = products.find(p => p.id === item.id);
      if (product && item.quantity > 0) {
        total += product.price * item.quantity;
      }
    });
    return total;
  };

  const calculateShipping = (orderItems: OrderItem[]) => {
    const total = calculateTotal(orderItems);
    if (total >= 350) return 0;
    const hasHKStyle = orderItems.some(item => 
      item.id === 'hk-style-radish' && item.quantity >= 1
    );
    const hasTaro = orderItems.some(item => 
      item.id === 'taro-cake' && item.quantity >= 1
    );
    const hasEnoughOriginal = orderItems.some(item => 
      item.id === 'original-radish' && item.quantity >= 2
    );
    const hasEnoughPineapple = orderItems.some(item => 
      item.id === 'pineapple-tofu' && item.quantity >= 2
    );
    if (hasHKStyle || hasTaro || hasEnoughOriginal || hasEnoughPineapple) {
      return 0;
    }
    return 100;
  };

  const handleSubmit = async (
    orderItems: OrderItem[],
    customerInfo: CustomerInfo,
    termsChecked: boolean,
    setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>
  ) => {
    if (!validateForm(orderItems, customerInfo, termsChecked, setErrors)) {
      toast({
        title: "表單驗證失敗",
        description: "請檢查並填寫所有必填欄位。",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      console.log("Starting order submission process");
      
      const orderNumber = 'ORD' + Date.now().toString().slice(-8);
      const totalAmount = calculateTotal(orderItems);
      const shippingFee = calculateShipping(orderItems);
      const validOrderItems = orderItems.filter(item => item.quantity > 0);
      const orderId = crypto.randomUUID();
      
      console.log("Preparing order data:", {
        orderId,
        customerInfo,
        totalAmount,
        shippingFee,
        orderNumber
      });
      
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .insert({
          id: orderId,
          customer_name: customerInfo.name,
          customer_phone: customerInfo.phone,
          customer_address: customerInfo.address,
          delivery_method: customerInfo.deliveryMethod,
          contact_method: customerInfo.contactMethod,
          contact_name: customerInfo.contactName,
          delivery_date: customerInfo.deliveryDate,
          delivery_time: customerInfo.deliveryTime,
          payment_method: customerInfo.paymentMethod,
          special_instructions: customerInfo.notes || null,
          total_amount: totalAmount,
          shipping_fee: shippingFee,
          status: 'pending',
          order_number: orderNumber
        })
        .select();
      
      if (orderError) {
        console.error("Order creation error:", orderError);
        throw new Error(`訂單創建失敗: ${orderError.message}`);
      }
      
      console.log("Order created successfully:", orderData);
      
      const orderItemsToInsert = validOrderItems.map(item => {
        const product = products.find(p => p.id === item.id);
        return {
          id: crypto.randomUUID(),
          order_id: orderId,
          product_id: item.id,
          quantity: item.quantity,
          price_at_time: product ? product.price : 0
        };
      });
      
      console.log("Preparing order items:", orderItemsToInsert);
      
      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItemsToInsert);
      
      if (itemsError) {
        console.error("Order items creation error:", itemsError);
        throw new Error(`訂單項目儲存失敗: ${itemsError.message}`);
      }
      
      console.log('訂單提交成功', {
        orderNumber,
        items: validOrderItems,
        customer: customerInfo,
        total: totalAmount,
        shipping: shippingFee,
        grandTotal: totalAmount + shippingFee
      });
      
      toast({
        title: "訂單提交成功！",
        description: `您的訂單編號為：${orderNumber}。我們會盡快與您聯繫確認訂單細節。`,
      });
      
      setTimeout(() => {
        navigate('/');
      }, 3000);
      
    } catch (error) {
      console.error('訂單提交失敗:', error);
      toast({
        title: "訂單提交失敗",
        description: error instanceof Error ? error.message : "發生未知錯誤，請稍後再試。",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateForm = (
    orderItems: OrderItem[],
    customerInfo: CustomerInfo,
    termsChecked: boolean,
    setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>
  ) => {
    const newErrors: Record<string, string> = {};
    if (!customerInfo.name.trim()) newErrors.name = '請輸入姓名';
    if (!customerInfo.phone.trim()) {
      newErrors.phone = '請輸入電話號碼';
    } else if (!/^[0-9]{8,10}$/.test(customerInfo.phone.replace(/-/g, ''))) {
      newErrors.phone = '請輸入有效的電話號碼';
    }
    if (!customerInfo.address.trim()) newErrors.address = '請輸入送貨地址';
    if (!customerInfo.deliveryMethod) newErrors.deliveryMethod = '請選擇配送方式';
    if (!customerInfo.contactMethod) newErrors.contactMethod = '請選擇聯繫方式';
    if (!customerInfo.contactName.trim()) newErrors.contactName = '請輸入聯繫帳號名稱';
    if (!customerInfo.deliveryDate) newErrors.deliveryDate = '請選擇送貨日期';
    if (!customerInfo.deliveryTime) newErrors.deliveryTime = '請選擇送貨時間';
    
    const hasProducts = orderItems.some(item => item.quantity > 0);
    if (!hasProducts) newErrors.products = '請至少選擇一項產品';
    if (!termsChecked) newErrors.terms = '請勾選同意條款';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  return {
    isSubmitting,
    calculateTotal,
    calculateShipping,
    handleSubmit
  };
};
