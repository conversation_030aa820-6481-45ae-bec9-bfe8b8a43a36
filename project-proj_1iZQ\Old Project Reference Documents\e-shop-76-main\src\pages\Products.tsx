
import React from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import FloatingActions from '../components/FloatingActions';
import { products } from '../data/products';

const Products = () => {
  return (
    <>
      <Header />
      <main>
        {/* 頁面標題 */}
        <section className="bg-radish py-12">
          <div className="container-custom">
            <h1 className="text-3xl md:text-4xl font-bold text-radish-darkest text-center">
              產品資訊
            </h1>
            <p className="text-center mt-4 max-w-2xl mx-auto">
              堅持使用新鮮食材，傳承傳統製作工藝，呈現最原始的美味。每一款產品都是我們用心製作的成果。
            </p>
          </div>
        </section>

        {/* 產品列表 */}
        <section className="py-16">
          <div className="container-custom">
            <div className="grid gap-8">
              {products.map((product, index) => (
                <div key={product.id} className={`bg-white rounded-lg shadow-md overflow-hidden ${index % 2 !== 0 ? 'md:flex-row-reverse' : ''} md:flex`}>
                  <div className="md:w-2/5">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-64 md:h-full object-cover"
                    />
                  </div>
                  <div className="p-6 md:w-3/5">
                    <h2 className="text-2xl font-bold mb-2 text-radish-darkest">{product.name}</h2>
                    <p className="text-gray-700 mb-4">{product.description}</p>
                    
                    <div className="bg-radish-light p-4 rounded-lg mb-4">
                      <h3 className="font-semibold text-radish-darker mb-2">食材成分</h3>
                      <ul className="list-disc list-inside space-y-1">
                        {product.ingredients.map((ingredient, i) => (
                          <li key={i}>{ingredient}</li>
                        ))}
                      </ul>
                      {product.isVegan && (
                        <p className="mt-2 text-green-600 font-medium">※ 純素食可用</p>
                      )}
                    </div>
                    
                    <div className="flex flex-wrap gap-4 justify-between items-center mb-4">
                      <div>
                        <h3 className="font-semibold text-radish-darker">價格</h3>
                        <p className="text-2xl font-bold text-radish-darkest">${product.price}</p>
                      </div>
                      <div>
                        <h3 className="font-semibold text-radish-darker">運費資訊</h3>
                        <p>{product.shippingInfo}</p>
                      </div>
                    </div>
                    
                    <div className="bg-gray-100 p-4 rounded-lg mb-6">
                      <h3 className="font-semibold text-radish-darker mb-2">保存方式</h3>
                      <p>{product.storageInfo}</p>
                    </div>
                    
                    <Link 
                      to={`/order?product=${product.id}`} 
                      className="btn-primary inline-block"
                    >
                      立即訂購
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 訂購說明 */}
        <section className="py-16 bg-radish-light">
          <div className="container-custom">
            <h2 className="section-title">訂購與配送資訊</h2>
            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="section-subtitle">訂購須知</h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>請提前1-2天預訂，以確保新鮮製作</li>
                    <li>可透過網站、LINE或電話訂購</li>
                    <li>訂購滿$350免運費</li>
                    <li>原味蘿蔔糕購買2樣以上免運費</li>
                    <li>港式蘿蔔糕與芋頭糕1樣即可免運費</li>
                    <li>鳳梨豆腐乳購買2瓶以上免運費</li>
                  </ul>
                </div>
                <div>
                  <h3 className="section-subtitle">配送資訊</h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>雲林地區可提供當日配送服務</li>
                    <li>外縣市透過宅配配送，通常需要1-2天</li>
                    <li>商品皆使用保冷包裝，確保新鮮</li>
                    <li>若有特殊配送需求，請在訂購時備註</li>
                  </ul>
                </div>
              </div>
              <div className="mt-8 text-center">
                <Link to="/order" className="btn-primary">
                  立即訂購
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <FloatingActions />
      <Footer />
    </>
  );
};

export default Products;
