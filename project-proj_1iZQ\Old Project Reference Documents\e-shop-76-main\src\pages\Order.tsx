
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import FloatingActions from '../components/FloatingActions';
import { products } from '../data/products';
import { Button } from "@/components/ui/button";
import ProductList from '../components/order/ProductList';
import OrderSummary from '../components/order/OrderSummary';
import CustomerInfoForm from '../components/order/CustomerInfoForm';
import OrderTerms from '../components/order/OrderTerms';
import { useOrderSubmission } from '@/hooks/useOrderSubmission';
import { useOrderForm } from '@/hooks/useOrderForm';

const Order = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const productId = searchParams.get('product');
  const { isSubmitting, calculateTotal, calculateShipping, handleSubmit } = useOrderSubmission();
  const {
    orderItems,
    setOrderItems,
    customerInfo,
    errors,
    setErrors,
    termsChecked,
    setTermsChecked,
    updateQuantity,
    handleCustomerInfoChange,
  } = useOrderForm(productId);

  useEffect(() => {
    const initialItems = products.map(product => ({
      id: product.id,
      quantity: product.id === productId ? 1 : 0
    }));
    setOrderItems(initialItems);
  }, [productId, setOrderItems]);

  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();
    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const formattedDate = date.toISOString().split('T')[0];
      const displayDate = `${date.getMonth() + 1}月${date.getDate()}日`;
      dates.push({ value: formattedDate, label: displayDate });
    }
    return dates;
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(orderItems, customerInfo, termsChecked, setErrors);
  };

  return (
    <>
      <Header />
      <main className="bg-radish-light min-h-screen pb-24 sm:pb-12 pt-3 sm:pt-12">
        <div className="container-custom">
          <div className="max-w-lg sm:max-w-5xl mx-auto bg-white rounded-lg shadow-lg p-3 sm:p-6 md:p-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-radish-darkest mb-4 sm:mb-6 text-center">線上訂購</h1>
            <form onSubmit={handleFormSubmit}>
              <ProductList
                orderItems={orderItems}
                updateQuantity={updateQuantity}
                errors={errors}
              />
              <OrderSummary
                orderItems={orderItems}
                calculateTotal={() => calculateTotal(orderItems)}
                calculateShipping={() => calculateShipping(orderItems)}
              />
              <CustomerInfoForm
                customerInfo={customerInfo}
                errors={errors}
                handleCustomerInfoChange={handleCustomerInfoChange}
                getAvailableDates={getAvailableDates}
              />
              <OrderTerms checked={termsChecked} setChecked={setTermsChecked} />
              {errors.terms && <p className="text-red-500 text-xs mb-2">{errors.terms}</p>}
              <div className="text-center mt-4">
                <Button
                  type="submit"
                  className="btn-primary w-full max-w-xs text-lg py-3 sm:min-w-[200px]"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? '處理中...' : '提交訂單'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </main>
      <FloatingActions />
      <Footer />
    </>
  );
};

export default Order;
