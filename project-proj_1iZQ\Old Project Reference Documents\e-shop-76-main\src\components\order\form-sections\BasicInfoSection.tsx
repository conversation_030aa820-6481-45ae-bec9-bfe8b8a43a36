
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface BasicInfoSectionProps {
  customerInfo: {
    name: string;
    phone: string;
    address: string;
  };
  errors: Record<string, string>;
  handleCustomerInfoChange: (name: string, value: string) => void;
}

const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  customerInfo,
  errors,
  handleCustomerInfoChange,
}) => {
  return (
    <>
      <div className="space-y-1">
        <Label htmlFor="name">姓名 *</Label>
        <Input
          id="name"
          name="name"
          value={customerInfo.name}
          onChange={(e) => handleCustomerInfoChange("name", e.target.value)}
          className={errors.name ? "border-red-500" : ""}
          spellCheck={false}
          autoComplete="name"
        />
        {errors.name && <p className="text-red-500 text-xs">{errors.name}</p>}
      </div>
      
      <div className="space-y-1">
        <Label htmlFor="phone">電話 *</Label>
        <Input
          id="phone"
          name="phone"
          value={customerInfo.phone}
          onChange={(e) => handleCustomerInfoChange("phone", e.target.value)}
          className={errors.phone ? "border-red-500" : ""}
          inputMode="tel"
          autoComplete="tel"
        />
        {errors.phone && <p className="text-red-500 text-xs">{errors.phone}</p>}
      </div>

      <div className="space-y-1 sm:col-span-2">
        <Label htmlFor="address">送貨地址 *</Label>
        <Input
          id="address"
          name="address"
          value={customerInfo.address}
          onChange={(e) => handleCustomerInfoChange("address", e.target.value)}
          className={errors.address ? "border-red-500" : ""}
          autoComplete="street-address"
        />
        {errors.address && <p className="text-red-500 text-xs">{errors.address}</p>}
      </div>
    </>
  );
};

export default BasicInfoSection;
