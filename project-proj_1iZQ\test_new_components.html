<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新組件測試 - 融氏古早味手工蘿蔔糕</title>
    
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/process.css" rel="stylesheet">
    <link href="styles/reviews.css" rel="stylesheet">
</head>
<body>
    <div id="root"></div>

    <script type="text/babel" src="utils/orderUtils.js"></script>
    <script type="text/babel" src="components/ProcessSection.js"></script>
    <script type="text/babel" src="components/OrderInstructions.js"></script>
    <script type="text/babel" src="components/CustomerReviews.js"></script>

    <script type="text/babel">
        function TestApp() {
            return (
                <div className="min-h-screen bg-gray-50">
                    <div className="container mx-auto py-8">
                        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
                            新組件測試頁面
                        </h1>
                        
                        <div className="space-y-8">
                            <div className="bg-white rounded-lg shadow-lg p-6">
                                <h2 className="text-xl font-bold mb-4 text-gray-800">製作過程組件</h2>
                                <ProcessSection />
                            </div>
                            
                            <div className="bg-white rounded-lg shadow-lg p-6">
                                <h2 className="text-xl font-bold mb-4 text-gray-800">訂購說明組件</h2>
                                <OrderInstructions />
                            </div>
                            
                            <div className="bg-white rounded-lg shadow-lg p-6">
                                <h2 className="text-xl font-bold mb-4 text-gray-800">客戶評價組件</h2>
                                <CustomerReviews />
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<TestApp />);
    </script>
</body>
</html>
