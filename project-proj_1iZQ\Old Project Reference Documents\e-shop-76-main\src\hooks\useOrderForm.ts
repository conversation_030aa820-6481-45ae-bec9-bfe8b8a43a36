
import { useState } from 'react';

export interface CustomerInfo {
  name: string;
  phone: string;
  address: string;
  deliveryMethod: string;
  contactMethod: string;
  contactName: string;
  deliveryDate: string;
  deliveryTime: string;
  paymentMethod: string;
  notes: string;
}

export interface OrderItem {
  id: string;
  quantity: number;
}

export const useOrderForm = (productId: string | null) => {
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    address: '',
    deliveryMethod: '宅配到府',
    contactMethod: '臉書粉絲團',
    contactName: '',
    deliveryDate: '',
    deliveryTime: '',
    paymentMethod: '貨到付款',
    notes: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [termsChecked, setTermsChecked] = useState(false);

  const updateQuantity = (id: string, quantity: number) => {
    setOrderItems(prev => 
      prev.map(item => 
        item.id === id ? { ...item, quantity: Math.max(0, quantity) } : item
      )
    );
  };

  const handleCustomerInfoChange = (name: string, value: string) => {
    setCustomerInfo(prev => ({ ...prev, [name]: value }));
    
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  return {
    orderItems,
    setOrderItems,
    customerInfo,
    errors,
    setErrors,
    termsChecked,
    setTermsChecked,
    updateQuantity,
    handleCustomerInfoChange,
  };
};
