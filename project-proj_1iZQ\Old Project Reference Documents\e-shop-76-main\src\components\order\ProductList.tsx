
import React from 'react';
import { products } from '../../data/products';

interface ProductListProps {
  orderItems: { id: string; quantity: number }[];
  updateQuantity: (id: string, quantity: number) => void;
  errors?: { [key: string]: string };
}
const ProductList: React.FC<ProductListProps> = ({ orderItems, updateQuantity, errors }) => {
  return (
    <div className="mb-6 sm:mb-8">
      <h2 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-4 pb-1 sm:pb-2 border-b border-radish">選擇產品</h2>
      {errors?.products && (
        <p className="text-red-500 mb-2">{errors.products}</p>
      )}
      <div className="space-y-4 sm:space-y-6">
        {products.map(product => (
          <div key={product.id} className="flex flex-col sm:flex-row items-start sm:items-center p-3 sm:p-4 border border-gray-200 rounded-lg">
            <div className="w-full sm:w-24 h-24 rounded-md overflow-hidden mb-3 sm:mb-0 mr-0 sm:mr-4 shrink-0">
              <img 
                src={product.image} 
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-grow mb-2 sm:mb-0 text-left">
              <h3 className="font-semibold text-radish-darker text-base">{product.name}</h3>
              <p className="text-xs sm:text-sm text-gray-600 mt-1">{product.description.slice(0, 60)}...</p>
              <div className="flex items-center mt-2">
                <span className="font-bold text-radish-darkest">${product.price}</span>
                <span className="text-xs sm:text-sm text-gray-500 ml-2">/ 份</span>
              </div>
            </div>
            <div className="flex items-center mt-2 sm:mt-0">
              <button 
                type="button"
                className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center border hover:bg-gray-200"
                onClick={() => updateQuantity(product.id, 
                  (orderItems.find(item => item.id === product.id)?.quantity || 0) - 1
                )}
                aria-label="減少數量"
              >
                -
              </button>
              <input 
                type="number"
                min="0"
                value={orderItems.find(item => item.id === product.id)?.quantity || 0}
                onChange={(e) => updateQuantity(product.id, parseInt(e.target.value) || 0)}
                className="w-10 text-center mx-2 border border-gray-300 rounded"
              />
              <button 
                type="button"
                className="w-8 h-8 rounded-full bg-radish flex items-center justify-center border hover:bg-radish-dark"
                onClick={() => updateQuantity(product.id, 
                  (orderItems.find(item => item.id === product.id)?.quantity || 0) + 1
                )}
                aria-label="增加數量"
              >
                +
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProductList;
