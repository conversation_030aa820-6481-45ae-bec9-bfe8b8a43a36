
import React from "react";
import BasicInfoSection from "./form-sections/BasicInfoSection";
import DeliverySection from "./form-sections/DeliverySection";
import ContactAndPaymentSection from "./form-sections/ContactAndPaymentSection";
import { type CustomerInfo } from "@/hooks/useOrderForm";

interface CustomerInfoFormProps {
  customerInfo: CustomerInfo;
  errors: Record<string, string>;
  handleCustomerInfoChange: (name: string, value: string) => void;
  getAvailableDates: () => { value: string; label: string }[];
}

const CustomerInfoForm: React.FC<CustomerInfoFormProps> = ({
  customerInfo,
  errors,
  handleCustomerInfoChange,
  getAvailableDates,
}) => {
  return (
    <div className="mb-6 sm:mb-8">
      <h2 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-4 pb-1 sm:pb-2 border-b border-radish">顧客資訊</h2>
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
        <BasicInfoSection
          customerInfo={customerInfo}
          errors={errors}
          handleCustomerInfoChange={handleCustomerInfoChange}
        />
        <DeliverySection
          customerInfo={customerInfo}
          errors={errors}
          handleCustomerInfoChange={handleCustomerInfoChange}
          getAvailableDates={getAvailableDates}
        />
        <ContactAndPaymentSection
          customerInfo={customerInfo}
          errors={errors}
          handleCustomerInfoChange={handleCustomerInfoChange}
        />
      </div>
    </div>
  );
};

export default CustomerInfoForm;
