
import React from 'react';
import { products } from '../../data/products';
import { type OrderItem } from '@/hooks/useOrderForm';

interface OrderSummaryProps {
  orderItems: OrderItem[];
  calculateTotal: () => number;
  calculateShipping: () => number;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ orderItems, calculateTotal, calculateShipping }) => {
  return (
    <div className="mt-4 sm:mt-6 bg-radish-light p-3 sm:p-4 rounded-lg">
      <h3 className="font-semibold mb-1 sm:mb-2">訂單摘要</h3>
      <div className="space-y-1 sm:space-y-2 text-sm">
        {orderItems.map(item => {
          const product = products.find(p => p.id === item.id);
          if (product && item.quantity > 0) {
            return (
              <div key={item.id} className="flex justify-between">
                <span>{product.name} x {item.quantity}</span>
                <span>${product.price * item.quantity}</span>
              </div>
            );
          }
          return null;
        })}
        <div className="border-t border-gray-300 pt-1 sm:pt-2 mt-1 sm:mt-2">
          <div className="flex justify-between">
            <span>小計</span>
            <span>${calculateTotal()}</span>
          </div>
          <div className="flex justify-between">
            <span>運費</span>
            <span>${calculateShipping()}</span>
          </div>
          <div className="flex justify-between font-bold text-base sm:text-lg mt-1 sm:mt-2">
            <span>總計</span>
            <span>${calculateTotal() + calculateShipping()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
