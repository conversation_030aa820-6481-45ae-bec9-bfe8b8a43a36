
import React from 'react';
import { Link } from 'react-router-dom';
import { PhoneCall, Clock, MapPin, ShoppingCart } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <header className="bg-radish-light shadow-md">
      <div className="container-custom">
        {/* 行動裝置：聯絡資訊隱藏，縮短導覽區間距 */}
        <div className="py-2 border-b border-radish text-sm hidden md:flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <PhoneCall className="w-4 h-4 mr-2 text-radish-darker" />
              <span>0933-477226 / 0968-789607</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2 text-radish-darker" />
              <span>週一至週日 9:00-22:00</span>
            </div>
            <div className="flex items-center">
              <MapPin className="w-4 h-4 mr-2 text-radish-darker" />
              <span>雲林縣西螺鎮中山路302-3號</span>
            </div>
          </div>
        </div>

        <div className="py-3 sm:py-4 flex justify-between items-center">
          <Link to="/" className="flex items-center">
            <div className="text-xl sm:text-2xl md:text-3xl font-bold text-radish-darkest leading-tight">
              融氏手工蘿蔔糕
            </div>
          </Link>
          <nav className="hidden md:flex space-x-4 sm:space-x-6">
            <Link to="/" className="text-radish-darker hover:text-radish-darkest font-medium">首頁</Link>
            <Link to="/products" className="text-radish-darker hover:text-radish-darkest font-medium">產品資訊</Link>
            <Link to="/order" className="text-radish-darker hover:text-radish-darkest font-medium">線上訂購</Link>
            <Link to="/contact" className="text-radish-darker hover:text-radish-darkest font-medium">聯絡我們</Link>
            <Link to="/about" className="text-radish-darker hover:text-radish-darkest font-medium">關於我們</Link>
          </nav>
          <div className="flex items-center space-x-2 sm:space-x-2">
            <Link to="/order" className="btn-primary flex items-center px-3 sm:px-6 py-1.5 sm:py-2 text-base">
              <ShoppingCart className="w-5 h-5 mr-1.5 sm:mr-2" />
              立即訂購
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
