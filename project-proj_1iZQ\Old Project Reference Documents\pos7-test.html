<html w-tid="0" lang="zh"><head w-tid="1"><style>html{min-height:100%}.ctxmenu{position:fixed;border:1px solid #999;padding:2px 0;box-shadow:#aaa 3px 3px 3px;background:#fff;margin:0;z-index:9999;overflow-y:auto;font:15px Verdana, sans-serif;box-sizing:border-box}.ctxmenu li{margin:1px 0;display:block;position:relative;user-select:none}.ctxmenu li.heading{font-weight:bold;margin-left:-5px}.ctxmenu li span{display:block;padding:2px 20px;cursor:default}.ctxmenu li a{color:inherit;text-decoration:none}.ctxmenu li.icon{padding-left:15px}.ctxmenu img.icon{position:absolute;width:18px;left:10px;top:2px}.ctxmenu li.disabled{color:#ccc}.ctxmenu li.divider{border-bottom:1px solid #aaa;margin:5px 0}.ctxmenu li.interactive:hover{background:rgba(0, 0, 0, .1)}.ctxmenu li.submenu::after{content:"";position:absolute;display:block;top:0;bottom:0;right:.4em;margin:auto .1rem auto auto;border-right:1px solid #000;border-top:1px solid #000;transform:rotate(45deg);width:.3rem;height:.3rem}.ctxmenu li.submenu.disabled::after{border-color:#ccc}</style><base href="" simulated-href="http://localhost:8000" w-tid="2">
  <link rel="stylesheet" href="https://api.websim.ai/fonts.css?v=c28a079b58fcc8ef7fc65153283118998ae82519" media="print" onload="this.media='all'">
  <meta charset="UTF-8" w-tid="5">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" w-tid="6">
  <link rel="icon" type="image/png" href="/images/IMG_3843.png.webp">
  <link rel="preconnect" href="https://connect.facebook.net">
  <link rel="canonical" href="https://767780.xyz/pos7-test.html">
  <meta property="og:title" content="融氏古早味手工蘿蔔糕">
  <meta property="og:description" content="純米漿製作，手工用心，美味蘿蔔糕，立即訂購！">
  <meta property="og:image" content="https://767780.xyz/images/IMG_3825.jpg.webp">
  <meta property="og:url" content="https://767780.xyz/pos7-test.html">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <title w-tid="7">融氏古早味手工蘿蔔糕</title>
    <style w-tid="8">
      :root {
        --primary-color: #d35400;
        --secondary-color: #e67e22;
        --accent-color: #27ae60;
        --text-color: #2c3e50;
        --light-bg: #fff5eb;
        --card-bg: #fff;
        --highlight: #e74c3c;
        --border-radius: 12px;
        --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        --gradient-accent: linear-gradient(135deg, var(--accent-color), #2ecc71);
        --text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: 'Noto Sans TC', "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #fff5eb 0%, #f8f0e3 50%, #fff5eb 100%);
        min-height: 100vh;
        scroll-behavior: smooth;
      }

      /* 載入動畫 */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--light-bg);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.5s ease;
      }

      .loading-overlay.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 導航欄樣式 */
      .navbar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: rgba(255, 245, 235, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        transition: var(--transition);
        transform: translateY(-100%);
      }

      .navbar.visible {
        transform: translateY(0);
      }

      .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60px;
      }

      .nav-brand {
        display: flex;
        align-items: center;
        height: 40px;
      }

      .nav-brand img {
        height: 40px;
        width: auto;
        object-fit: contain;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        transition: var(--transition);
      }

      .nav-brand img:hover {
        transform: scale(1.05);
        filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
      }

      .nav-menu {
        display: flex;
        gap: 2rem;
        align-items: center;
      }

      .nav-link {
        color: var(--text-color);
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        transition: var(--transition);
        position: relative;
      }

      .nav-link:hover {
        color: var(--primary-color);
        background: rgba(211, 84, 0, 0.1);
        transform: translateY(-2px);
      }

      .nav-toggle {
        display: none;
        flex-direction: column;
        cursor: pointer;
        gap: 4px;
      }

      .nav-toggle span {
        width: 25px;
        height: 3px;
        background: var(--primary-color);
        transition: var(--transition);
        border-radius: 2px;
      }

      .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
      }

      .nav-toggle.active span:nth-child(2) {
        opacity: 0;
      }

      .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
      }

      /* 滾動進度條 */
      .scroll-progress {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 4px;
        background: var(--gradient-primary);
        z-index: 1001;
        transition: width 0.1s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .banner {
        width: 100%;
        background: var(--gradient-primary);
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem 0;
        overflow: hidden;
        box-shadow: var(--shadow);
        position: relative;
      }

      .banner::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shimmer 3s infinite;
      }

      @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }

      .banner img {
        width: 100%;
        height: auto;
        max-height: 200px;
        object-fit: contain;
        filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        transition: var(--transition);
        position: relative;
        z-index: 1;
      }

      .banner img:hover {
        transform: scale(1.02);
      }

      section {
        padding: 1.5em;
        max-width: 1000px;
        margin: 0 auto;
      }

      .card {
        background: linear-gradient(135deg, var(--card-bg) 0%, #fafafa 100%);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: var(--transition);
        border: 1px solid rgba(211, 84, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
      }

      .card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .card:hover::before {
        left: 100%;
      }

      .section-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-color);
        text-align: center;
        margin-bottom: 1.5rem;
        position: relative;
        padding-bottom: 0.8rem;
        text-shadow: var(--text-shadow);
        letter-spacing: 0.5px;
      }

      .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background: var(--gradient-primary);
        border-radius: 4px;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }

      .promotion {
        background: linear-gradient(135deg, #fff 0%, #fff5eb 50%, #fff 100%);
        color: var(--text-color);
        padding: 2rem;
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
        border: 3px dashed var(--secondary-color);
        animation: glow 3s infinite alternate;
        position: relative;
        overflow: hidden;
      }

      .promotion::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(211,84,0,0.05) 0%, transparent 70%);
        animation: rotate 10s linear infinite;
      }

      @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .promotion h2 {
        font-size: 1.5rem;
        color: var(--primary-color);
        text-align: center;
      }

      .pricing h2, .storage-info h2 {
        font-size: 1.5rem;
        text-align: center;
        color: var(--primary-color);
        margin-bottom: 1rem;
      }

      .pricing p {
        font-size: 1.1rem;
        text-align: left;
        margin-bottom: 1rem;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        border-left: 4px solid var(--secondary-color);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
      }

      .pricing p::before {
        content: "💰";
        margin-right: 0.5rem;
        font-size: 1.2rem;
      }

      .pricing p:hover {
        transform: translateX(10px);
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      /* 價格表特殊樣式 */
      .pricing p {
        margin-bottom: 0.6rem; /* 減少間距 */
      }

      .pricing-note {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 4px solid var(--accent-color);
        padding: 1.2rem;
        border-radius: 12px;
        margin-top: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .pricing-note p {
        margin-bottom: 0.4rem;
        font-size: 1rem;
        line-height: 1.5;
        border: none;
        background: none;
        padding: 0;
        border-radius: 0;
      }

      .pricing-note p::before {
        content: "📝";
        margin-right: 0.5rem;
      }

      .pricing-note p:hover {
        transform: none;
        background: none;
        box-shadow: none;
      }

      .product-item {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--accent-color);
        margin-bottom: 1.2rem;
        padding: 1.2rem 1.5rem 1.2rem 3rem;
        position: relative;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        border-radius: 12px;
        transition: var(--transition);
        border: 2px solid transparent;
        overflow: hidden;
      }

      .product-item::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-primary);
        transition: var(--transition);
      }

      .product-item:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-3px) scale(1.02);
        border-color: var(--secondary-color);
      }

      .product-item:hover::before {
        width: 8px;
      }

      .product-item::after {
        content: "🥕";
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.5rem;
        transition: var(--transition);
      }

      .product-item:hover::after {
        transform: translateY(-50%) scale(1.2) rotate(10deg);
      }

      .product-description {
        font-size: 0.95rem;
        font-weight: normal;
        margin-top: 0.3rem;
      }

      .storage-info {
        font-size: 1.1rem;
        background: linear-gradient(135deg, #f1f8e9, #e8f5e9);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        margin-bottom: 1.5rem;
        border: none;
      }

      .storage-info li {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        list-style-type: none;
        position: relative;
        padding-left: 1.5rem;
      }

      .storage-info li::before {
        content: "✓";
        position: absolute;
        left: 0;
        color: var(--accent-color);
        font-weight: bold;
      }

      @keyframes glow {
        from { box-shadow: 0 0 5px var(--secondary-color); }
        to { box-shadow: 0 0 15px var(--primary-color); }
      }

      .pricing, .contact, .customer-reviews {
        background: linear-gradient(135deg, #fff, #fff8ee);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        margin-bottom: 1.5rem;
        margin-top: 1rem;
        border: none;
      }

      .photo-gallery-container {
        width: 100%;
        margin: 1.5rem 0;
      }

      .photo-row-container {
        position: relative;
        margin: 1.5rem 0;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow);
      }

      .photo-row {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 0;
        padding: 1rem 0.5rem;
        background: linear-gradient(135deg, #f9f0e3, #f5e5d0);
        position: relative;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch; /* 為iOS設備提供平滑滾動 */
      }

      .gallery-item {
        display: inline-block;
        width: 280px;
        height: 280px;
        margin-right: 12px;
        background-color: var(--card-bg);
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        background-size: cover;
        background-position: center;
      }

      .gallery-item:hover {
        transform: scale(1.03);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
      }

      .gallery-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        will-change: transform; /* 優化動畫性能 */
        transition: transform 0.5s ease;
      }

      .gallery-item:hover img {
        transform: scale(1.05);
      }

      .order-system {
        width: 100%;
        border: none;
        min-height: 800px;
        margin-bottom: 1rem;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow);
      }

      .order-section {
        background: linear-gradient(135deg, #2980b9, #3498db);
        border: none;
        border-radius: var(--border-radius);
        margin: 1.5rem 0;
        box-shadow: var(--shadow);
        overflow: hidden;
      }

      .order-section h2 {
        text-align: center;
        font-size: 1.8rem;
        font-weight: 700;
        color: #fff;
        margin: 1rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }

      .order-section p {
        font-size: 1.1rem;
        font-weight: 500;
        color: #fff;
        line-height: 1.5;
        padding: 1rem 1.5rem;
        margin-bottom: 1rem;
        background-color: rgba(255,255,255,0.1);
        border-radius: 8px;
      }

      footer {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: #fff;
        margin-top: 2rem;
      }

      /* 浮動按鈕群組 */
      .floating-buttons {
        position: fixed;
        bottom: 30px;
        right: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;
        z-index: 1000;
      }

      .float-button {
        width: 60px;
        height: 60px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        cursor: pointer;
        transition: var(--transition);
        text-decoration: none;
        border: none;
        position: relative;
        overflow: hidden;
      }

      .float-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
      }

      .float-button:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 8px 25px rgba(0,0,0,0.4);
      }

      .float-button:hover::before {
        left: 100%;
      }

      .back-to-top {
        background: var(--gradient-accent);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
      }

      .back-to-top.visible {
        opacity: 1;
        visibility: visible;
      }

      .float-button svg {
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
      }

      .float-button span {
        font-size: 12px;
        text-align: center;
        font-weight: 500;
      }

      .photo-row::-webkit-scrollbar {
        display: none;
      }

      .photo-row {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .social-buttons {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
        margin: 2rem 0;
      }

      .social-link {
        transition: all 0.3s ease;
        display: block;
        background-color: var(--card-bg);
        padding: 0.8rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
      }

      .social-link:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
      }

      .social-link img {
        max-width: 180px;
        height: auto;
        border-radius: 8px;
      }

      .order-notice {
        background-color: #fff9e6;
        border-left: 4px solid var(--secondary-color);
        color: var(--text-color);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        margin: 2rem 0;
        box-shadow: var(--shadow);
      }

      .process-image {
        width: 95%;
        max-width: 1000px;
        margin: 1.5rem auto;
        display: block;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        transition: transform 0.3s ease;
      }

      .process-image:hover {
        transform: scale(1.01);
      }

      .contact p.note {
        text-align: center;
        color: #666;
        font-style: italic;
        margin: 0.5rem 0;
      }

      .contact p.address-note {
        text-align: center;
        color: #666;
        margin-top: 0.3rem;
        font-size: 0.9rem;
      }

      .product-image {
        width: 100%;
        max-width: 600px;
        height: auto;
        margin: 1.5rem auto;
        display: block;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        transition: transform 0.3s ease;
      }

      .product-image:hover {
        transform: scale(1.02);
      }

      .nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        cursor: pointer;
        z-index: 10;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
        touch-action: manipulation;  /* 防止手機上的雙擊縮放 */
        -webkit-touch-callout: none; /* 禁用iOS長按菜單 */
        -webkit-user-select: none;   /* 禁用文字選擇 */
        user-select: none;
      }

      .prev-button {
        left: 10px;
      }

      .next-button {
        right: 10px;
      }

      .nav-button:hover {
        background: rgba(0, 0, 0, 0.7);
      }

      /* 防止所有圖片和按鈕的觸控放大 */
      img, button {
        touch-action: manipulation;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }

      /* 新增 Tawk.to 按鈕的位置樣式 */
      #tawk-button {
        position: fixed;
        bottom: 100px;  /* 比 float-button 高一些 */
        right: 30px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #4CAF50, #45a049);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        cursor: pointer;
        z-index: 1000;
        transition: transform 0.3s ease;
      }

      #tawk-button:hover {
        transform: scale(1.1);
      }

      /* 移除舊的浮動按鈕樣式，統一使用新的浮動按鈕群組 */

      /* 新增RWD響應式設計 */
      @media screen and (max-width: 768px) {
        .nav-menu {
          position: fixed;
          top: 60px;
          left: -100%;
          width: 100%;
          height: calc(100vh - 60px);
          background: rgba(255, 245, 235, 0.98);
          backdrop-filter: blur(10px);
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          padding-top: 2rem;
          transition: var(--transition);
          gap: 1rem;
        }

        .nav-menu.active {
          left: 0;
        }

        .nav-toggle {
          display: flex;
        }

        .nav-link {
          font-size: 1.2rem;
          padding: 1rem 2rem;
          width: 80%;
          text-align: center;
          border-radius: 10px;
          background: rgba(255, 255, 255, 0.8);
          margin: 0.5rem 0;
        }

        .banner img {
          max-height: 170px;
        }

        .process-image {
          width: 95%;
          max-height: 250px;
        }

        .product-image {
          max-width: 100%;
        }

        .gallery-item {
          width: 220px;
          height: 220px;
        }

        .social-link img {
          max-width: 150px;
        }

        .pricing h2, .storage-info h2, .promotion h2 {
          font-size: 20px;
        }

        .product-item {
          font-size: 16px;
        }

        .product-description {
          font-size: 13px;
        }
      }

      @media screen and (max-width: 480px) {
        .banner img {
          max-height: 120px;
        }

        section {
          padding: 0.5em;
        }

        .gallery-item {
          width: 180px;
          height: 180px;
        }

        .social-link img {
          max-width: 120px;
        }

        .floating-buttons {
          right: 15px;
        }

        .float-button {
          width: 50px;
          height: 50px;
        }

        .floating-buttons {
          gap: 10px;
        }

        .order-section h2 {
          font-size: 24px;
        }

        .order-section p {
          font-size: 16px;
        }
      }
    </style>
  </head>
  <body w-tid="9">
    <!-- 滾動進度條 -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- 載入動畫 -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="spinner"></div>
    </div>

    <!-- 導航欄 -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <img src="/images/LOGO-1.webp" alt="融氏古早味" loading="eager">
        </div>
        <div class="nav-menu" id="navMenu">
          <a href="#products" class="nav-link">商品介紹</a>
          <a href="#pricing" class="nav-link">價格表</a>
          <a href="#gallery" class="nav-link">製作過程</a>
          <a href="#order" class="nav-link">線上訂購</a>
          <a href="#contact" class="nav-link">聯絡我們</a>
        </div>
        <div class="nav-toggle" id="navToggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <div class="banner" w-tid="10">
      <img src="/images/IMG_3840.png.webp" alt="古早味手工蘿蔔糕 Logo" w-tid="11" width="100%" height="200">
    </div>

    <img src="/images/FireShot003.jpg.webp" alt="製作過程" class="process-image" w-tid="12" width="95%" height="800" loading="eager">

    <section w-tid="13">
      <div class="promotion" w-tid="14" id="products">
        <h2 w-tid="15" class="section-title">商品說明</h2>
        <img src="/images/IMG_3825.jpg.webp" alt="商品照片" class="product-image" width="100%" height="300" loading="eager">
        <div class="product-item" w-tid="16">古早味蘿蔔糕[純素食可用]
          <div class="product-description" w-tid="17" style="color: #d32f2f">1條約1500克(+-5%)<br w-tid="18" style="color: #d32f2f">
          (水、在來米、白蘿蔔、鹽、糖、味精、地瓜粉、麵粉)</div>
        </div>

        <div class="product-item" w-tid="19">古早味芋頭糕[純素食可用]
          <div class="product-description" w-tid="20" style="color: #d32f2f">1條約1500克(+-5%)<br w-tid="21" style="color: #d32f2f">
          (水、在來米、芋頭、鹽、糖、味精、麵粉)</div>
        </div>

        <div class="product-item" w-tid="22">港式蘿蔔糕[每批限量]
          <div class="product-description" w-tid="23" style="color: #d32f2f">1條約1500克(+-5%)<br w-tid="24" style="color: #d32f2f">
          (火腿肉、自炸豬油、嚴選蝦米、溫體豬肉、100%純胡椒粉、自炸油蔥酥)<br w-tid="25" style="color: #d32f2f">
          (水、在來米、白蘿蔔、鹽、糖、味精、地瓜粉、麵粉)</div>
        </div>
      </div>

      <div class="pricing" w-tid="26" id="pricing">
        <h2 w-tid="27" class="section-title">商品價格表</h2>
        <p w-tid="28">★原味蘿蔔$250 (購買二條即可免運費)</p>
        <p w-tid="29">★港式口味$350 (購買一條即可免運費)</p>
        <p w-tid="30">★芋頭口味$350 (購買一條即可免運費)</p>
        <p w-tid="31">★鳳梨豆腐乳$300 (滿2瓶免運費)</p>

        <div class="pricing-note">
          <p w-tid="32">每條蘿蔔糕都約台斤2斤半(1500克左右)</p>
          <p w-tid="33">以上歡迎參考看看</p>
          <p w-tid="34">貨到付款或轉帳都可以</p>
          <p w-tid="35">(購買總額滿$350即可免運費)</p>
        </div>
      </div>

      <div class="storage-info" w-tid="36">
        <h2 w-tid="37">保存說明</h2>
        <p w-tid="38">1. 冷藏保存方式：</p>
        <ul w-tid="39">
          <li w-tid="40">未拆封可保存7-8天</li>
          <li w-tid="41">最佳保存溫度：2℃~5℃</li>
          <li w-tid="42">注意：純米漿製作不可冷凍</li>
        </ul>
        <p w-tid="43">2. 拆封後保存：</p>
        <ul w-tid="44">
          <li w-tid="45">建議3天內食用完畢</li>
          <li w-tid="46">可用乾淨紙巾或布包裹，若有濕氣須及時更換</li>
        </ul>
        <p w-tid="47">3. 品質檢查：</p>
        <ul w-tid="48">
          <li w-tid="49">無黏液且無酸味即為正常</li>
          <li w-tid="50">避免悶熱及過多濕氣</li>
        </ul>
        <p w-tid="51">特別提醒：退冰後再冷藏會縮短保存期限</p>
      </div>

      <div class="social-buttons" w-tid="52">
        <a href="https://www.facebook.com/wadelala/" class="social-link" target="_blank" w-tid="53">
          <img src="/images/IMG_3845.png.webp" alt="Facebook Fan Page" w-tid="54">
        </a>

      </div>



      <div class="photo-gallery-container" w-tid="57" id="gallery">
        <h2 class="section-title">製作過程展示</h2>
        <div class="photo-row-container">
          <button class="nav-button prev-button" onclick="prevSlide(0)">❮</button>
          <div class="photo-row" id="row-0">
            <div class="gallery-item"><img src="/images/IMG_3827.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3829.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3830.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3828.jpg.webp" alt="製作過程" loading="lazy"></div>
          </div>
          <button class="nav-button next-button" onclick="nextSlide(0)">❯</button>
        </div>
        <div class="photo-row-container">
          <button class="nav-button prev-button" onclick="prevSlide(1)">❮</button>
          <div class="photo-row" id="row-1">
            <div class="gallery-item"><img src="/images/IMG_3834.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3831.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3832.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3833.jpg.webp" alt="製作過程" loading="lazy"></div>
          </div>
          <button class="nav-button next-button" onclick="nextSlide(1)">❯</button>
        </div>
        <div class="photo-row-container">
          <button class="nav-button prev-button" onclick="prevSlide(2)">❮</button>
          <div class="photo-row" id="row-2">
            <div class="gallery-item"><img src="/images/IMG_3835.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3836.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3837.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3838.jpg.webp" alt="製作過程" loading="lazy"></div>
          </div>
          <button class="nav-button next-button" onclick="nextSlide(2)">❯</button>
        </div>
        <div class="photo-row-container">
          <button class="nav-button prev-button" onclick="prevSlide(3)">❮</button>
          <div class="photo-row" id="row-3">
            <div class="gallery-item"><img src="/images/IMG_3841.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3842.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3839.jpg.webp" alt="製作過程" loading="lazy"></div>
            <div class="gallery-item"><img src="/images/IMG_3840.jpg.webp" alt="製作過程" loading="lazy"></div>
          </div>
          <button class="nav-button next-button" onclick="nextSlide(3)">❯</button>
        </div>
      </div>

      <div class="order-section" w-tid="94" id="order">
        <h2 w-tid="95" class="section-title" style="color: white;">訂購須知</h2>
        <p w-tid="96">若您有指定日期到貨，有少許可能會早或晚1-2天到貨的狀況發生!
          <br>若超過您的訂貨日才到貨的話，客人您可以拒收沒關係，因為是我們沒辦法讓產品如期交到您手上。有預先轉帳付款的我們會退費唷。</p>
<iframe id="orderSystem" class="order-system" src="https://mayfaire.us.kg/pos8-test2.php" w-tid="100" style="height: 800px;"></iframe>
      </div>

      <div class="contact" w-tid="101" id="contact">
        <h2 w-tid="102" class="section-title">聯絡我們</h2>
        <p w-tid="103">營業時間：週一至週日 09:00-22:00</p>
        <p class="note" w-tid="104">＜每日售完為止＞</p>
        <p w-tid="105">地址：雲林縣西螺鎮中山路302-3號</p>
        <p class="address-note" w-tid="106">(戶政事務所跟西光幼稚園中間)</p>
        <p w-tid="107">電話：0933-477226&nbsp;&nbsp;&nbsp;0968-789607</p>
        <p w-tid="108">※為確保商品新鮮度，建議提前1-2天預訂</p>
      </div>
    </section>

    <!-- 浮動按鈕群組 -->
    <div class="floating-buttons">
      <!-- 回到頂部按鈕 -->
      <button class="float-button back-to-top" id="backToTop" onclick="scrollToTop()">
        <svg viewBox="0 0 24 24" fill="white">
          <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
        </svg>
        <span style="font-size: 10px;">TOP</span>
      </button>

      <!-- 訂單查詢按鈕 -->
      <a href="/order_query.php" target="_blank" class="float-button" style="background: linear-gradient(135deg, #FF9800, #F57C00);">
        <svg viewBox="0 0 24 24" fill="white">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
        <span style="font-size: 10px;">查詢</span>
      </a>

      <!-- Facebook Messenger按鈕 -->
      <a href="http://m.me/112463436907299" target="_blank" class="float-button" style="background: linear-gradient(135deg, #0084FF, #0066CC);">
        <svg viewBox="0 0 24 24" fill="white">
          <path d="M12 2C6.486 2 2 6.262 2 11.5c0 2.545 1.088 4.988 3 6.772v4.228l4.833-2.416c.697.173 1.417.266 2.167.266 5.514 0 10-4.262 10-9.5S17.514 2 12 2zm1.167 12.333l-3.833-4.167-7.167 4.167 6.5-7 3.833 4.167 7.167-4.167-6.5 7z"/>
        </svg>
        <span style="font-size: 10px;">FB</span>
      </a>

      <!-- LINE按鈕 -->
      <a href="https://line.me/R/ti/p/@218oxpgu" target="_blank" class="float-button" style="background: linear-gradient(135deg, #00B900, #00A000);">
        <svg viewBox="0 0 24 24" fill="white">
          <path d="M24 10.304c0-5.369-5.383-9.738-12-9.738-6.616 0-12 4.369-12 9.738 0 4.814 4.269 8.846 10.036 9.608.391.084.922.258 1.057.592.121.303.079.778.039 1.085l-.171 1.027c-.053.303-.242 1.186 1.039.647 1.281-.54 6.911-4.069 9.428-6.967 1.739-1.907 2.572-3.843 2.572-5.992z"/>
        </svg>
        <span style="font-size: 10px;">LINE</span>
      </a>

      <!-- 線上訂購按鈕 -->
      <button class="float-button" onclick="scrollToOrder()">
        <svg viewBox="0 0 24 24" fill="white">
          <path d="M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"/>
        </svg>
        <span style="font-size: 10px;">訂購</span>
      </button>
    </div>
  <!--
    <div id="tawk-button" onclick="Tawk_API.toggle()">
        <svg viewBox="0 0 24 24" width="24" height="24" fill="white">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z"/>
            <path d="M6 12h12v2H6zm0-3h12v2H6zm0-3h12v2H6z"/>
        </svg>
        <span style="font-size: 12px; margin-top: 4px;">線上客服</span>
    </div>
  -->
    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous"
            src="https://connect.facebook.net/zh_TW/sdk.js#xfbml=1&version=v18.0&appId=1578924339681706"
            nonce="random_nonce">
    </script>



    <footer w-tid="113">
      <p w-tid="114">© 2023 融氏古早味手工蘿蔔糕 | 版權所有</p>
    </footer>

    <script w-tid="118">
      // 載入動畫
      window.addEventListener('load', function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        setTimeout(() => {
          loadingOverlay.classList.add('hidden');
        }, 1000);
      });

      // 導航欄功能和滾動進度條
      window.addEventListener('scroll', function() {
        const navbar = document.getElementById('navbar');
        const backToTop = document.getElementById('backToTop');
        const scrollProgress = document.getElementById('scrollProgress');

        // 計算滾動進度
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        // 更新進度條
        scrollProgress.style.width = scrollPercent + '%';

        // 顯示/隱藏導航欄和回到頂部按鈕
        if (window.scrollY > 100) {
          navbar.classList.add('visible');
          backToTop.classList.add('visible');
        } else {
          navbar.classList.remove('visible');
          backToTop.classList.remove('visible');
        }
      });

      // 手機導航選單切換
      const navToggle = document.getElementById('navToggle');
      const navMenu = document.getElementById('navMenu');

      navToggle.addEventListener('click', function() {
        navToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
      });

      // 平滑滾動功能
      function scrollToOrder() {
        document.getElementById('order').scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }

      function scrollToContact() {
        document.getElementById('contact').scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }

      function scrollToTop() {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }

      // 導航連結點擊事件
      document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);

          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }

          // 關閉手機選單
          navToggle.classList.remove('active');
          navMenu.classList.remove('active');
        });
      });

      window.addEventListener('message', function(e) {
        const iframe = document.getElementById('orderSystem');
        if (e.data && e.data.height) {
          iframe.style.height = e.data.height + 'px';
        }
      });

      const rowStates = [
        { currentIndex: 0 },
        { currentIndex: 0 },
        { currentIndex: 0 },
        { currentIndex: 0 }
      ];

      function nextSlide(rowIndex) {
        const row = document.getElementById(`row-${rowIndex}`);
        const items = row.querySelectorAll('.gallery-item');
        rowStates[rowIndex].currentIndex = (rowStates[rowIndex].currentIndex + 1) % items.length;
        const scrollAmount = items[rowStates[rowIndex].currentIndex].offsetLeft - row.offsetLeft;
        row.scrollTo({
          left: scrollAmount,
          behavior: 'smooth'
        });
      }

      function prevSlide(rowIndex) {
        const row = document.getElementById(`row-${rowIndex}`);
        const items = row.querySelectorAll('.gallery-item');
        rowStates[rowIndex].currentIndex = (rowStates[rowIndex].currentIndex - 1 + items.length) % items.length;
        const scrollAmount = items[rowStates[rowIndex].currentIndex].offsetLeft - row.offsetLeft;
        row.scrollTo({
          left: scrollAmount,
          behavior: 'smooth'
        });
      }

      // 添加觸控滑動支援
      document.addEventListener('DOMContentLoaded', function() {
        const photoRows = document.querySelectorAll('.photo-row');

        photoRows.forEach((row, rowIndex) => {
          let startX, moved;

          row.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            moved = false;
          }, { passive: true });

          row.addEventListener('touchmove', function() {
            moved = true;
          }, { passive: true });

          row.addEventListener('touchend', function(e) {
            if (!moved) return;

            const endX = e.changedTouches[0].clientX;
            const diff = startX - endX;

            if (Math.abs(diff) > 50) { // 確保是有意義的滑動
              if (diff > 0) {
                nextSlide(rowIndex);
              } else {
                prevSlide(rowIndex);
              }
            }
          }, { passive: true });
        });

        // 優化圖片加載和動畫效果
        const lazyImages = document.querySelectorAll('img[loading="lazy"]');
        const animatedElements = document.querySelectorAll('.card, .product-item, .pricing p');

        if ('IntersectionObserver' in window) {
          // 圖片懶加載觀察器
          const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';

                img.onload = () => {
                  img.style.opacity = '1';
                };

                img.src = img.src; // 觸發加載
                imageObserver.unobserve(img);
              }
            });
          });

          // 元素動畫觀察器
          const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                animationObserver.unobserve(entry.target);
              }
            });
          }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
          });

          lazyImages.forEach(img => {
            imageObserver.observe(img);
          });

          animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            animationObserver.observe(el);
          });
        }

        // 根據裝置調整圖片畫廊大小
        function adjustGallerySize() {
          const galleryItems = document.querySelectorAll('.gallery-item');
          const width = window.innerWidth;

          if (width <= 480) {
            galleryItems.forEach(item => {
              item.style.width = '180px';
              item.style.height = '180px';
            });
          } else if (width <= 768) {
            galleryItems.forEach(item => {
              item.style.width = '220px';
              item.style.height = '220px';
            });
          } else {
            galleryItems.forEach(item => {
              item.style.width = '280px';
              item.style.height = '280px';
            });
          }
        }

        // 初始調整和視窗大小變化時調整
        adjustGallerySize();
        window.addEventListener('resize', adjustGallerySize);
      });
    </script>

    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous"
            src="https://connect.facebook.net/zh_TW/sdk.js#xfbml=1&version=v18.0&appId=1578924339681706"
            nonce="random_nonce">
    </script>

    <!--Start of Tawk.to Script
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/6767ce1749e2fd8dfefbd0a3/1iho0obh3';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!-End of Tawk.to Script-->

  </body></html>