function ProductCard({ product }) {
    try {
        return (
            <div className="bg-white rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" data-name="product-card">
                <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-64 object-cover"
                    data-name="product-image"
                />
                <div className="p-6" data-name="product-info">
                    <h3 className="text-xl font-bold mb-2 text-gray-800" data-name="product-name">
                        {product.name}
                    </h3>
                    <p className="text-gray-600 mb-4" data-name="product-description">
                        {product.description}
                    </p>
                    <div className="mb-4" data-name="product-ingredients">
                        <h4 className="font-semibold text-gray-700 mb-2">成分：</h4>
                        <p className="text-gray-600">{product.ingredients}</p>
                    </div>
                    <div className="flex justify-between items-center" data-name="product-price-section">
                        <span className="text-2xl font-bold text-red-600" data-name="product-price">
                            NT$ {product.price}
                        </span>
                        <div className="text-sm text-gray-500" data-name="product-shipping">
                            {product.shippingNote}
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ProductCard component error:', error);
        reportError(error);
        return null;
    }
}
