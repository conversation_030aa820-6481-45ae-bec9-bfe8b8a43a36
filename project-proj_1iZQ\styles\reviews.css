/* 客戶評價區塊樣式 */
.customer-reviews-section {
    position: relative;
    overflow: hidden;
}

.customer-reviews-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #fef2f2 0%, #fff7ed 50%, #fef2f2 100%);
    z-index: -1;
}

/* 評價卡片 */
.review-card {
    position: relative;
    border-radius: 16px;
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    overflow: hidden;
}

.review-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc2626 0%, #f59e0b 50%, #dc2626 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.review-card:hover::before {
    transform: scaleX(1);
}

.review-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #fca5a5;
}

/* 顧客頭像 */
.review-card .w-12.h-12 {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.review-card:hover .w-12.h-12 {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}



/* 產品標籤 */
.review-card .bg-red-100 {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;
    transition: all 0.3s ease;
}

.review-card:hover .bg-red-100 {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    transform: scale(1.05);
}

/* 評價內容 */
.review-card p {
    position: relative;
    font-style: italic;
    line-height: 1.6;
    color: #4b5563;
}

.review-card p::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: -8px;
    font-size: 2rem;
    color: #dc2626;
    opacity: 0.3;
    font-family: serif;
}

/* 有用按鈕 */
.review-card button {
    transition: all 0.3s ease;
    border-radius: 6px;
    padding: 4px 8px;
}

.review-card button:hover {
    background: #fef2f2;
    color: #dc2626;
    transform: scale(1.05);
}

/* 統計資訊 */
.customer-reviews-section .text-2xl.font-bold.text-red-600 {
    background: linear-gradient(135deg, #dc2626 0%, #f59e0b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
}

/* 底部行動呼籲 */
.customer-reviews-section .bg-white.rounded-lg.shadow-lg {
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
}

.customer-reviews-section .bg-white.rounded-lg.shadow-lg:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1);
}

/* 按鈕樣式 */
.customer-reviews-section .bg-red-600 {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.customer-reviews-section .bg-red-600::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.customer-reviews-section .bg-red-600:hover::before {
    left: 100%;
}

.customer-reviews-section .bg-red-600:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .customer-reviews-section {
        padding: 3rem 0;
    }

    .review-card {
        padding: 1.25rem;
    }

    .customer-reviews-section .grid {
        gap: 1rem;
    }

    .customer-reviews-section .text-2xl {
        font-size: 1.5rem;
    }
}

@media (max-width: 640px) {
    .customer-reviews-section .grid.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
    }

    /* 保持統計資訊在行動裝置也並排顯示 */
    .customer-reviews-section .flex.justify-center.items-center {
        flex-direction: row !important;
        justify-content: center;
        align-items: center;
        gap: 0;
    }

    .customer-reviews-section .flex.justify-center.items-center > div {
        margin: 0;
        flex: 1;
        max-width: 33.333%;
    }
}

/* 動畫效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.review-card {
    animation: slideInUp 0.6s ease forwards;
}

.review-card:nth-child(1) { animation-delay: 0.1s; }
.review-card:nth-child(2) { animation-delay: 0.2s; }
.review-card:nth-child(3) { animation-delay: 0.3s; }
.review-card:nth-child(4) { animation-delay: 0.4s; }
.review-card:nth-child(5) { animation-delay: 0.5s; }
.review-card:nth-child(6) { animation-delay: 0.6s; }

/* 標題樣式增強 */
.customer-reviews-section .section-title {
    position: relative;
    display: inline-block;
}

.customer-reviews-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #dc2626 0%, #f59e0b 50%, #dc2626 100%);
    border-radius: 2px;
}

/* 浮動效果 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.review-card:nth-child(odd) {
    animation: slideInUp 0.6s ease forwards, float 6s ease-in-out infinite;
}

.review-card:nth-child(even) {
    animation: slideInUp 0.6s ease forwards, float 6s ease-in-out infinite reverse;
}
