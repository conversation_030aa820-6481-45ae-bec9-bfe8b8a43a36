
import React, { useState } from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import FloatingActions from '../components/FloatingActions';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { PhoneCall, Mail, MapPin, Clock, MessageSquare, Facebook } from 'lucide-react';

const Contact = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除錯誤訊息
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = '請輸入姓名';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = '請輸入Email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '請輸入有效的Email地址';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = '請輸入電話號碼';
    } else if (!/^[0-9]{8,10}$/.test(formData.phone.replace(/-/g, ''))) {
      newErrors.phone = '請輸入有效的電話號碼';
    }
    
    if (!formData.message.trim()) {
      newErrors.message = '請輸入訊息內容';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "表單驗證失敗",
        description: "請檢查並填寫所有必填欄位。",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    // 模擬發送訊息
    setTimeout(() => {
      console.log('訊息已發送', formData);
      
      toast({
        title: "訊息已送出",
        description: "感謝您的留言，我們會盡快回覆您。",
      });
      
      // 重置表單
      setFormData({
        name: '',
        email: '',
        phone: '',
        message: ''
      });
      
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <>
      <Header />
      <main className="min-h-screen">
        {/* 頁面標題 */}
        <section className="bg-radish py-12">
          <div className="container-custom">
            <h1 className="text-3xl md:text-4xl font-bold text-radish-darkest text-center">
              聯絡我們
            </h1>
            <p className="text-center mt-4 max-w-2xl mx-auto">
              有任何問題或建議，歡迎隨時與我們聯繫。我們很樂意傾聽您的想法，並為您提供協助。
            </p>
          </div>
        </section>

        {/* 聯絡資訊與表單 */}
        <section className="py-16">
          <div className="container-custom">
            <div className="grid md:grid-cols-2 gap-12">
              {/* 聯絡資訊 */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-radish-darkest">聯絡資訊</h2>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <PhoneCall className="w-6 h-6 text-radish-darker mr-4 mt-1" />
                    <div>
                      <h3 className="font-semibold text-radish-darker">電話</h3>
                      <p className="mt-1">0933-477226</p>
                      <p>0968-789607</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Clock className="w-6 h-6 text-radish-darker mr-4 mt-1" />
                    <div>
                      <h3 className="font-semibold text-radish-darker">營業時間</h3>
                      <p className="mt-1">週一至週日 9:00-22:00</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <MapPin className="w-6 h-6 text-radish-darker mr-4 mt-1" />
                    <div>
                      <h3 className="font-semibold text-radish-darker">地址</h3>
                      <p className="mt-1">雲林縣西螺鎮中山路302-3號</p>
                      <a href="https://goo.gl/maps/123" className="text-radish-darker hover:underline mt-2 inline-block">
                        在Google地圖上查看
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Mail className="w-6 h-6 text-radish-darker mr-4 mt-1" />
                    <div>
                      <h3 className="font-semibold text-radish-darker">Email</h3>
                      <p className="mt-1"><EMAIL></p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h3 className="font-semibold text-radish-darker mb-4">關注我們</h3>
                  <div className="flex space-x-4">
                    <a
                      href="#"
                      className="bg-blue-600 hover:bg-blue-700 w-10 h-10 rounded-full flex items-center justify-center transition-colors"
                      aria-label="Facebook"
                    >
                      <Facebook className="w-5 h-5 text-white" />
                    </a>
                    <a
                      href="#"
                      className="bg-green-600 hover:bg-green-700 w-10 h-10 rounded-full flex items-center justify-center transition-colors"
                      aria-label="Line"
                    >
                      <MessageSquare className="w-5 h-5 text-white" />
                    </a>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h3 className="font-semibold text-radish-darker mb-4">店鋪位置</h3>
                  <div className="rounded-lg overflow-hidden shadow-md">
                    <iframe 
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3646.5728465034727!2d120.4596317!3d23.7978533!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x346e9ce66944a595%3A0xb32da6fca7cd0220!2zNjQ45a6c5p6X57ij6KW_5aKv6Y6u5Lit5bGx6LevMzAy!5e0!3m2!1szh-TW!2stw!4v1663925526025!5m2!1szh-TW!2stw" 
                      className="w-full h-80"
                      style={{ border: 0 }}
                      allowFullScreen 
                      loading="lazy"
                      title="店鋪位置"
                    ></iframe>
                  </div>
                </div>
              </div>
              
              {/* 聯絡表單 */}
              <div>
                <h2 className="text-2xl font-bold mb-6 text-radish-darkest">留言給我們</h2>
                
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <form onSubmit={handleSubmit}>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="name">姓名 *</Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className={errors.name ? "border-red-500" : ""}
                        />
                        {errors.name && (
                          <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label htmlFor="email">Email *</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleChange}
                          className={errors.email ? "border-red-500" : ""}
                        />
                        {errors.email && (
                          <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label htmlFor="phone">電話 *</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          className={errors.phone ? "border-red-500" : ""}
                        />
                        {errors.phone && (
                          <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                        )}
                      </div>
                      
                      <div>
                        <Label htmlFor="message">訊息內容 *</Label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          className={`min-h-[150px] ${errors.message ? "border-red-500" : ""}`}
                        />
                        {errors.message && (
                          <p className="text-red-500 text-sm mt-1">{errors.message}</p>
                        )}
                      </div>
                      
                      <Button
                        type="submit"
                        className="btn-primary w-full"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? '發送中...' : '發送訊息'}
                      </Button>
                    </div>
                  </form>
                </div>
                
                <div className="mt-8 bg-radish-light p-6 rounded-lg">
                  <h3 className="font-semibold text-radish-darker mb-4">常見問題</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium">如何訂購產品？</h4>
                      <p className="text-sm mt-1">您可以透過網站、LINE或電話進行訂購。</p>
                    </div>
                    <div>
                      <h4 className="font-medium">需要提前多久預訂？</h4>
                      <p className="text-sm mt-1">建議提前1-2天預訂，以確保新鮮製作。</p>
                    </div>
                    <div>
                      <h4 className="font-medium">有提供外送服務嗎？</h4>
                      <p className="text-sm mt-1">我們提供雲林地區的當日配送服務，其他地區則透過宅配配送。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <FloatingActions />
      <Footer />
    </>
  );
};

export default Contact;
