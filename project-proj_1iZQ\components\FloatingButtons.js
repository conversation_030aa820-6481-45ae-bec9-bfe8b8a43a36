/**
 * 右側浮動按鈕組件
 * 包含回到頂部、訂單查詢、Facebook、LINE、線上訂購等功能按鈕
 */
function FloatingButtons() {
    const [showBackToTop, setShowBackToTop] = React.useState(false);

    React.useEffect(() => {
        const handleScroll = () => {
            setShowBackToTop(window.scrollY > 300);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    const scrollToOrder = () => {
        const orderSection = document.getElementById('order');
        if (orderSection) {
            orderSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    };

    try {
        return (
            <div className="floating-buttons" data-name="floating-buttons">
                {/* 回到頂部按鈕 */}
                {showBackToTop && (
                    <button 
                        className="float-button back-to-top" 
                        onClick={scrollToTop}
                        data-name="back-to-top-button"
                        title="回到頂部"
                    >
                        <svg viewBox="0 0 24 24" fill="white" className="w-6 h-6">
                            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
                        </svg>
                        <span className="text-xs">TOP</span>
                    </button>
                )}

                {/* 訂單查詢按鈕 */}
                <a 
                    href="/admin" 
                    target="_blank" 
                    className="float-button order-query" 
                    data-name="order-query-button"
                    title="訂單查詢"
                >
                    <svg viewBox="0 0 24 24" fill="white" className="w-6 h-6">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    <span className="text-xs">查詢</span>
                </a>

                {/* Facebook Messenger按鈕 */}
                <a 
                    href="https://www.facebook.com/wadelala/" 
                    target="_blank" 
                    className="float-button facebook" 
                    data-name="facebook-button"
                    title="Facebook 粉絲頁"
                >
                    <svg viewBox="0 0 24 24" fill="white" className="w-6 h-6">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    <span className="text-xs">FB</span>
                </a>

                {/* LINE按鈕 */}
                <a 
                    href="https://line.me/R/ti/p/@218oxpgu" 
                    target="_blank" 
                    className="float-button line" 
                    data-name="line-button"
                    title="LINE 官方帳號"
                >
                    <svg viewBox="0 0 24 24" fill="white" className="w-6 h-6">
                        <path d="M24 10.304c0-5.369-5.383-9.738-12-9.738-6.616 0-12 4.369-12 9.738 0 4.814 4.269 8.846 10.036 9.608.391.084.922.258 1.057.592.121.303.079.778.039 1.085l-.171 1.027c-.053.303-.242 1.186 1.039.647 1.281-.54 6.911-4.069 9.428-6.967 1.739-1.907 2.572-3.843 2.572-5.992z"/>
                    </svg>
                    <span className="text-xs">LINE</span>
                </a>

                {/* 線上訂購按鈕 */}
                <button 
                    className="float-button order" 
                    onClick={scrollToOrder}
                    data-name="order-button"
                    title="線上訂購"
                >
                    <svg viewBox="0 0 24 24" fill="white" className="w-6 h-6">
                        <path d="M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"/>
                    </svg>
                    <span className="text-xs">訂購</span>
                </button>
            </div>
        );
    } catch (error) {
        console.error('FloatingButtons component error:', error);
        reportError(error);
        return null;
    }
}
