
import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import FloatingActions from '../components/FloatingActions';

const About = () => {
  return (
    <>
      <Header />
      <main className="min-h-screen">
        {/* 頁面標題 */}
        <section className="bg-radish py-12">
          <div className="container-custom">
            <h1 className="text-3xl md:text-4xl font-bold text-radish-darkest text-center">
              關於我們
            </h1>
            <p className="text-center mt-4 max-w-2xl mx-auto">
              傳承傳統味道，堅持手工製作，用心將台灣的美食文化傳承下去。
            </p>
          </div>
        </section>

        {/* 品牌故事 */}
        <section className="py-16">
          <div className="container-custom">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-2xl font-bold mb-6 text-radish-darkest">品牌故事</h2>
                <p className="mb-4">
                  我們的故事始於多年前，從一個家庭廚房開始。創辦人秉持著對傳統美食的熱愛與堅持，從小在父母親的教導下學習製作蘿蔔糕的技藝。
                </p>
                <p className="mb-4">
                  在雲林這片純樸的土地上，我們選用當地最新鮮的食材，堅持手工製作每一塊蘿蔔糕，保留最原始的風味與口感。
                </p>
                <p>
                  多年來，我們的蘿蔔糕已成為許多在地人的共同回憶，也是許多外地遊客必嚐的美食。我們將這份傳統的美味透過現代的方式傳遞給更多人，讓這份傳統的好味道繼續流傳下去。
                </p>
              </div>
              <div className="rounded-lg overflow-hidden shadow-lg">
                <img
                  src="/placeholder.svg"
                  alt="品牌故事"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </section>

        {/* 我們的堅持 */}
        <section className="py-16 bg-radish-light">
          <div className="container-custom">
            <h2 className="text-2xl font-bold mb-12 text-radish-darkest text-center">
              我們的堅持
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="w-20 h-20 mx-auto mb-4 bg-radish rounded-full flex items-center justify-center">
                  <span className="text-3xl font-bold text-radish-darker">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-4 text-radish-darkest">新鮮食材</h3>
                <p>
                  堅持選用最新鮮的蘿蔔與芋頭，每日採購，確保食材的新鮮度與品質。
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="w-20 h-20 mx-auto mb-4 bg-radish rounded-full flex items-center justify-center">
                  <span className="text-3xl font-bold text-radish-darker">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-4 text-radish-darkest">手工製作</h3>
                <p>
                  每一塊蘿蔔糕都是純手工製作，從原料處理到蒸製，都堅持傳統工法，不添加防腐劑。
                </p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="w-20 h-20 mx-auto mb-4 bg-radish rounded-full flex items-center justify-center">
                  <span className="text-3xl font-bold text-radish-darker">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-4 text-radish-darkest">用心服務</h3>
                <p>
                  從訂購到送達，我們用心對待每一位顧客，確保您能享受到最好的產品與服務。
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* 製作過程 */}
        <section className="py-16">
          <div className="container-custom">
            <h2 className="text-2xl font-bold mb-12 text-radish-darkest text-center">
              製作過程
            </h2>
            
            <div className="relative">
              {/* 時間軸 */}
              <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-radish"></div>
              
              <div className="space-y-12">
                {/* 步驟1 */}
                <div className="flex flex-col md:flex-row items-center">
                  <div className="md:w-1/2 md:pr-12 md:text-right order-2 md:order-1">
                    <h3 className="text-xl font-semibold mb-2 text-radish-darker">選料</h3>
                    <p>
                      精選上等新鮮蘿蔔，確保每一根蘿蔔都保持最佳的品質與風味。
                    </p>
                  </div>
                  <div className="z-10 flex items-center justify-center w-12 h-12 bg-radish-dark rounded-full mb-4 md:mb-0 order-1 md:order-2">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <div className="md:w-1/2 md:pl-12 order-3 hidden md:block">
                    <img 
                      src="/placeholder.svg" 
                      alt="選料"
                      className="rounded-lg shadow-md h-48 w-full object-cover"
                    />
                  </div>
                </div>
                
                {/* 步驟2 */}
                <div className="flex flex-col md:flex-row items-center">
                  <div className="md:w-1/2 md:pr-12 hidden md:block order-1">
                    <img 
                      src="/placeholder.svg" 
                      alt="處理"
                      className="rounded-lg shadow-md h-48 w-full object-cover"
                    />
                  </div>
                  <div className="z-10 flex items-center justify-center w-12 h-12 bg-radish-dark rounded-full mb-4 md:mb-0 order-2">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <div className="md:w-1/2 md:pl-12 md:text-left order-3">
                    <h3 className="text-xl font-semibold mb-2 text-radish-darker">處理</h3>
                    <p>
                      將蘿蔔洗淨、切絲，與其他配料精心調配，準備製作米漿。
                    </p>
                  </div>
                </div>
                
                {/* 步驟3 */}
                <div className="flex flex-col md:flex-row items-center">
                  <div className="md:w-1/2 md:pr-12 md:text-right order-2 md:order-1">
                    <h3 className="text-xl font-semibold mb-2 text-radish-darker">製作</h3>
                    <p>
                      將調配好的米漿與蘿蔔絲混合，加入配料，準備下一步的蒸製。
                    </p>
                  </div>
                  <div className="z-10 flex items-center justify-center w-12 h-12 bg-radish-dark rounded-full mb-4 md:mb-0 order-1 md:order-2">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <div className="md:w-1/2 md:pl-12 order-3 hidden md:block">
                    <img 
                      src="/placeholder.svg" 
                      alt="製作"
                      className="rounded-lg shadow-md h-48 w-full object-cover"
                    />
                  </div>
                </div>
                
                {/* 步驟4 */}
                <div className="flex flex-col md:flex-row items-center">
                  <div className="md:w-1/2 md:pr-12 hidden md:block order-1">
                    <img 
                      src="/placeholder.svg" 
                      alt="蒸製"
                      className="rounded-lg shadow-md h-48 w-full object-cover"
                    />
                  </div>
                  <div className="z-10 flex items-center justify-center w-12 h-12 bg-radish-dark rounded-full mb-4 md:mb-0 order-2">
                    <span className="text-white font-bold">4</span>
                  </div>
                  <div className="md:w-1/2 md:pl-12 md:text-left order-3">
                    <h3 className="text-xl font-semibold mb-2 text-radish-darker">蒸製</h3>
                    <p>
                      使用傳統的大型蒸籠，以最適合的溫度與時間蒸製蘿蔔糕。
                    </p>
                  </div>
                </div>
                
                {/* 步驟5 */}
                <div className="flex flex-col md:flex-row items-center">
                  <div className="md:w-1/2 md:pr-12 md:text-right order-2 md:order-1">
                    <h3 className="text-xl font-semibold mb-2 text-radish-darker">冷卻包裝</h3>
                    <p>
                      蘿蔔糕蒸熟後，需要適當冷卻，然後進行切割與包裝，準備出貨。
                    </p>
                  </div>
                  <div className="z-10 flex items-center justify-center w-12 h-12 bg-radish-dark rounded-full mb-4 md:mb-0 order-1 md:order-2">
                    <span className="text-white font-bold">5</span>
                  </div>
                  <div className="md:w-1/2 md:pl-12 order-3 hidden md:block">
                    <img 
                      src="/placeholder.svg" 
                      alt="冷卻包裝"
                      className="rounded-lg shadow-md h-48 w-full object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 我們的團隊 */}
        <section className="py-16 bg-radish-light">
          <div className="container-custom">
            <h2 className="text-2xl font-bold mb-12 text-radish-darkest text-center">
              我們的團隊
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="h-64 overflow-hidden">
                    <img 
                      src="/placeholder.svg" 
                      alt={`團隊成員 ${item}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-semibold text-radish-darker">
                      {["曾老闆", "王師傅", "廖師傅"][item - 1]}
                    </h3>
                    <p className="text-gray-600 mt-1">
                      {["創辦人", "主廚", "廚藝總監"][item - 1]}
                    </p>
                    <p className="mt-4">
                      {[
                        "擁有30年製作蘿蔔糕經驗，對傳統美食充滿熱情。",
                        "專業廚師出身，精通各式傳統糕點製作。",
                        "堅持食材品質與製作工藝，追求最道地的口味。"
                      ][item - 1]}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>
      <FloatingActions />
      <Footer />
    </>
  );
};

export default About;
