
import React from "react";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface DeliverySectionProps {
  customerInfo: {
    deliveryMethod: string;
    deliveryDate: string;
    deliveryTime: string;
  };
  errors: Record<string, string>;
  handleCustomerInfoChange: (name: string, value: string) => void;
  getAvailableDates: () => { value: string; label: string }[];
}

const DeliverySection: React.FC<DeliverySectionProps> = ({
  customerInfo,
  errors,
  handleCustomerInfoChange,
  getAvailableDates,
}) => {
  return (
    <>
      <div className="space-y-1">
        <Label htmlFor="deliveryMethod">配送方式 *</Label>
        <Select 
          value={customerInfo.deliveryMethod}
          onValueChange={(value) => handleCustomerInfoChange("deliveryMethod", value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="請選擇配送方式" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="宅配到府">宅配到府</SelectItem>
            <SelectItem value="超商取貨">超商取貨</SelectItem>
          </SelectContent>
        </Select>
        {errors.deliveryMethod && (
          <p className="text-red-500 text-xs">{errors.deliveryMethod}</p>
        )}
      </div>

      <div className="space-y-1">
        <Label htmlFor="deliveryDate">送貨日期 *</Label>
        <Select 
          value={customerInfo.deliveryDate}
          onValueChange={(value) => handleCustomerInfoChange("deliveryDate", value)}
        >
          <SelectTrigger className={`w-full ${errors.deliveryDate ? "border-red-500" : ""}`}>
            <SelectValue placeholder="請選擇日期" />
          </SelectTrigger>
          <SelectContent>
            {getAvailableDates().map(date => (
              <SelectItem key={date.value} value={date.value}>
                {date.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.deliveryDate && (
          <p className="text-red-500 text-xs">{errors.deliveryDate}</p>
        )}
      </div>

      <div className="space-y-1">
        <Label htmlFor="deliveryTime">送貨時間 *</Label>
        <Select 
          value={customerInfo.deliveryTime}
          onValueChange={(value) => handleCustomerInfoChange("deliveryTime", value)}
        >
          <SelectTrigger className={`w-full ${errors.deliveryTime ? "border-red-500" : ""}`}>
            <SelectValue placeholder="請選擇時間" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="09:00-12:00">上午 (09:00-12:00)</SelectItem>
            <SelectItem value="12:00-15:00">中午 (12:00-15:00)</SelectItem>
            <SelectItem value="15:00-18:00">下午 (15:00-18:00)</SelectItem>
            <SelectItem value="18:00-21:00">晚上 (18:00-21:00)</SelectItem>
          </SelectContent>
        </Select>
        {errors.deliveryTime && (
          <p className="text-red-500 text-xs">{errors.deliveryTime}</p>
        )}
      </div>
    </>
  );
};

export default DeliverySection;
