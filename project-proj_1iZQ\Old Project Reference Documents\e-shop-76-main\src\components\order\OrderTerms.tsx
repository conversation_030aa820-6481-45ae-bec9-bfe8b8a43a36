
import React from 'react';
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface OrderTermsProps {
  checked: boolean;
  setChecked: React.Dispatch<React.SetStateAction<boolean>>;
}

const OrderTerms: React.FC<OrderTermsProps> = ({ checked, setChecked }) => {
  return (
    <div className="mb-6">
      <div className="flex items-start space-x-2">
        <Checkbox id="terms" checked={checked} onCheckedChange={val => setChecked(Boolean(val))} className="mt-1" />
        <Label htmlFor="terms" className="text-xs sm:text-sm leading-snug">
          我已閱讀並同意網站的
          <a href="#" className="text-radish-darker hover:underline">條款與條件</a>
          及
          <a href="#" className="text-radish-darker hover:underline">隱私政策</a>
        </Label>
      </div>
    </div>
  );
};

export default OrderTerms;
