
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { PhoneCall, Mail, MapPin, Clock, Facebook, MessageSquare } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-radish-darker text-white pt-10 pb-4 sm:pt-12 sm:pb-6">
      <div className="container-custom">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-8">
          {/* 聯絡資訊 */}
          <div className="mb-4 sm:mb-0">
            <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-4 text-white">聯絡資訊</h3>
            <div className="space-y-3">
              <div className="flex items-start">
                <PhoneCall className="w-5 h-5 mr-2 sm:mr-3 mt-1 flex-shrink-0" />
                <div>
                  <p>0933-477226</p>
                  <p>0968-789607</p>
                </div>
              </div>
              <div className="flex items-center">
                <Clock className="w-5 h-5 mr-2 sm:mr-3 flex-shrink-0" />
                <p>週一至週日 9:00-22:00</p>
              </div>
              <div className="flex items-start">
                <MapPin className="w-5 h-5 mr-2 sm:mr-3 mt-1 flex-shrink-0" />
                <p>雲林縣西螺鎮中山路302-3號</p>
              </div>
            </div>
          </div>
          {/* 快速連結 */}
          <div className="mb-4 sm:mb-0">
            <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-4 text-white">快速連結</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="hover:text-radish transition-colors">首頁</Link>
              </li>
              <li>
                <Link to="/products" className="hover:text-radish transition-colors">產品資訊</Link>
              </li>
              <li>
                <Link to="/order" className="hover:text-radish transition-colors">線上訂購</Link>
              </li>
              <li>
                <Link to="/contact" className="hover:text-radish transition-colors">聯絡我們</Link>
              </li>
              <li>
                <Link to="/about" className="hover:text-radish transition-colors">關於我們</Link>
              </li>
            </ul>
          </div>
          {/* 關注我們 */}
          <div>
            <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-4 text-white">關注我們</h3>
            <div className="flex space-x-2 sm:space-x-4 mb-3 sm:mb-4">
              <a
                href="#"
                className="bg-blue-600 hover:bg-blue-700 w-9 h-9 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="bg-green-600 hover:bg-green-700 w-9 h-9 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-colors"
                aria-label="Line"
              >
                <MessageSquare className="w-5 h-5" />
              </a>
            </div>
            <p className="text-xs sm:text-sm text-gray-300">
              透過社群媒體與我們聯繫，獲取最新的產品資訊和優惠訊息。
            </p>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-5 pt-4 text-center text-xs sm:text-sm text-gray-400">
          <p>&copy; {new Date().getFullYear()} 雲林手工蘿蔔糕. 版權所有.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
