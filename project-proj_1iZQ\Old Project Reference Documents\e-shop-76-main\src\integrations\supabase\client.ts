// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ccaqupcdjpkkjgfgkzje.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjYXF1cGNkanBra2pnZmdremplIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyODUzMzgsImV4cCI6MjA2MDg2MTMzOH0.BXbRt55boeN2N4wrw5YeUjHV8JfHllIkC03PRqxq5rs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);